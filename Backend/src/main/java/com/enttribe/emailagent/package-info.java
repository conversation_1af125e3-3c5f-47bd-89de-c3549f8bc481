/*
 * Copyright © 2023–2025 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of VisionWave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of VisionWave.
 */

/**
 * This package contains the main Email Agent application and all its core components.
 *
 * <p>The Email Agent is a Spring Boot application that provides comprehensive email and calendar
 * management functionality through Microsoft Graph API integration. This package serves as the root
 * package containing all application components.
 *
 * <p>Key components in this package include:
 *
 * <ul>
 *   <li>{@link com.enttribe.emailagent.EmailAgentApplication} - The main Spring Boot application
 *       class that serves as the entry point for the Email Agent service
 * </ul>
 *
 * <p>This package contains several sub-packages that organize the application functionality:
 *
 * <ul>
 *   <li>{@link com.enttribe.emailagent.constant} - Application constants and configuration values
 *   <li>{@link com.enttribe.emailagent.dao} - Data Access Objects for database operations
 *   <li>{@link com.enttribe.emailagent.dto} - Data Transfer Objects for API communication
 *   <li>{@link com.enttribe.emailagent.entity} - JPA entity classes for data persistence
 *   <li>{@link com.enttribe.emailagent.exception} - Custom exception classes and error handling
 *   <li>{@link com.enttribe.emailagent.filter} - HTTP filters for request processing
 *   <li>{@link com.enttribe.emailagent.rest} - REST controllers for API endpoints
 *   <li>{@link com.enttribe.emailagent.service} - Business logic service classes
 *   <li>{@link com.enttribe.emailagent.userinfo} - User context and information management
 *   <li>{@link com.enttribe.emailagent.utils} - Utility classes for common operations
 * </ul>
 *
 * <p>The application provides a comprehensive solution for email management, calendar integration,
 * meeting scheduling, and availability checking through secure REST API endpoints with proper
 * authentication and authorization.
 *
 * <AUTHOR>
 * @version 1.0.7
 * @since 1.0.0
 */
package com.enttribe.emailagent;
