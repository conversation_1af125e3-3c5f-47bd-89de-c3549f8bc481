/*
 * Copyright © 2023–2025 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 */

/**
 * This package contains HTTP filters that process incoming requests and provide cross-cutting
 * concerns for the Email Agent application.
 *
 * <p>The filters in this package handle request processing and include:
 *
 * <ul>
 *   <li>{@link com.enttribe.emailagent.filter.JwtTokenFilter} - JWT token authentication filter
 *       that:
 *       <ul>
 *         <li>Extracts and validates JWT tokens from HTTP headers
 *         <li>Parses token claims to extract user information
 *         <li>Sets user context for the current request
 *         <li>Handles multiple token formats and claim structures
 *         <li>Provides authentication for all API endpoints
 *       </ul>
 * </ul>
 *
 * <p>These filters implement Spring's filter chain pattern and extend OncePerRequestFilter to
 * ensure they are executed once per HTTP request. They provide security and authentication
 * functionality by intercepting requests before they reach the REST controllers.
 *
 * <p>The filters work in conjunction with the user context management system to provide seamless
 * authentication and user information availability throughout the application request lifecycle.
 *
 * <AUTHOR>
 * @version 1.0.7
 * @since 1.0.0
 */
package com.enttribe.emailagent.filter;
