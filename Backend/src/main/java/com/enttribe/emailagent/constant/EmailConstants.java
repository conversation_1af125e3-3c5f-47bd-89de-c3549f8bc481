package com.enttribe.emailagent.constant;

/**
 * Constants for email-related operations used throughout the email agent application.
 * This class defines various constants for date formats, HTTP headers, and response fields.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0.0
 */
public final class EmailConstants {

  // Date format constants
  public static final String YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss";
  public static final String YYYY_MM_DD_T_HH_MM_SS_Z = "yyyy-MM-dd'T'HH:mm:ss'Z'";

  // Time zone constants
  public static final String TIME_ZONE = "timeZone";

  // Meeting-related constants
  public static final String MEETING_START_TIME = "meetingStartTime";
  public static final String MEETING_END_TIME = "meetingEndTime";
  public static final String REQUIRED_ATTENDEES = "requiredAttendees";
  public static final String OPTIONAL_ATTENDEES = "optionalAttendees";
  public static final String MEETING_TYPE = "meetingType";
  public static final String LOCATION = "location";
  public static final String LOCATION_URL = "locationUri";
  public static final String SUBJECT = "subject";

  // HTTP header constants
  public static final String AUTHORIZATION = "Authorization";
  public static final String BEARER = "Bearer ";
  public static final String CONTENT_TYPE = "Content-Type";
  public static final String APPLICATION_JSON = "application/json";
  public static final String PREFER = "Prefer";

  // Email address constants
  public static final String ADDRESS = "address";
  public static final String EMAIL_ADDRESS = "emailAddress";
  public static final String DATE_TIME = "dateTime";
  public static final String VALUE = "value";
  public static final String TYPE = "type";

  // Response constants
  public static final String CONTENT = "content";
  public static final String SUCCESS = "success";
  public static final String FAILED = "failed";
  public static final String RESULT = "result";
  public static final String STATUS = "status";
  public static final String MESSAGE = "message";
  public static final String BODY = "body";
  public static final String ERROR = "error";

  public static final String REASON = "reason";
  public static final String UPDATED_EVENT_OBJECT = "updatedEventObject";
  public static final String SCHEDULE_EVENT_OBJECT = "scheduleEventObject";
  public static final String EVENT_ID = "eventId";
  public static final String COMMENT = "Comment";
  public static final String AWAY = "away";
  public static final String UNKNOWN = "unknown";
  public static final String DISPLAY_NAME = "displayName";
  public static final String WORKING_HOURS = "workingHours";
  public static final String AVAILABILITY_VIEW = "availabilityView";
  public static final String SCHEDULE_ID = "scheduleId";
  public static final String SCHEDULE_ITEMS = "scheduleItems";
  public static final String TO_RECIPIENTS = "ToRecipients";
  public static final String EMAIL_ADDRESS_OBJ = "EmailAddress";
  public static final String ADDRESS_OBJ = "Address";
  public static final String OUTLOOK_TIMEZONE = "outlook.timezone";
  public static final String UTC = "UTC";

  public static final String START_TIME = "startTime";
  public static final String END_TIME = "endTime";
  public static final String SLOT_DURATION = "slotDuration";
  public static final String MEETING_COUNT = "meetingCount";
  public static final String MEETINGS = "meetings";
  public static final String TEAMS = "Teams";
  public static final String AVAILABILITY_VIEW_INTERVAL = "availabilityViewInterval";
  public static final String SCHEDULES = "schedules";
  public static final String WORKING_FLAG = "workingFlag";
  public static final String USER_PRINCIPAL_NAME = "userPrincipalName";

  public static final String HTML = "HTML";
  public static final String START = "start";
  public static final String END = "end";
  public static final String ATTENDEES = "attendees";
  public static final String ORGANIZER = "organizer";
  public static final String HAS_ATTACHMENTS = "hasAttachments";
  public static final String IS_CANCELLED = "isCancelled";
  public static final String ONLINE_MEETING = "onlineMeeting";
  public static final String JOIN_URL = "joinUrl";
  public static final String IS_ONLINE_MEETING = "isOnlineMeeting";
  public static final String ONLINE_MEETING_PROVIDER = "onlineMeetingProvider";
  public static final String TEAMS_FOR_BUSINESS = "teamsForBusiness";
  public static final String RESCHEDULE_REASON = "rescheduleReason";
  public static final String REQUIRED = "required";
  public static final String OPTIONAL = "optional";

  public static final String STARTDATETIME = "startdatetime";
  public static final String ENDDATETIME = "enddatetime";
  public static final String START_DATE_TIME = "startDateTime";
  public static final String END_DATE_TIME = "endDateTime";

  public static final String INVALID_ARGUMENT_PROVIDED_FOR_FAILURELOGS_CREATION = "Invalid argument provided for FailureLogs creation";
  public static final String INVALID_ARGUMENT_PROVIDED = "Invalid argument provided";

  public static final String DATABASE_ACCESS_ERROR_WHILE_SAVING_FAILURELOGS = "Database access error while saving FailureLogs";
  public static final String DATABASE_ACCESS_ERROR = "Database access error";
  public static final String UNEXPECTED_ERROR_OCCURRED_WHILE_PROCESSING_FAILURELOGS = "Unexpected error occurred while processing FailureLogs";
  public static final String AN_UNEXPECTED_ERROR_OCCURRED = "An unexpected error occurred";
  public static final String FAILURELOGS_NOT_FOUND_FOR_THIS_ID = "FailureLog not found for this id :: ";



  
  // Audit logging constants
  public static final String GET_CALENDER_EVENT_EWS = "GET_CALENDER_EVENT_EWS";

  // Private constructor to prevent instantiation
  private EmailConstants() {
    throw new UnsupportedOperationException("utility class can not be instantiated");
  }
}
