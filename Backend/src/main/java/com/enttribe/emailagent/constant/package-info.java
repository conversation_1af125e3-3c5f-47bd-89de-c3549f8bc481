/*
 * Copyright © 2023–2025 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of VisionWave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of VisionWave.
 */

/**
 * This package contains constant classes that define application-wide constants and configuration
 * values for the Email Agent application.
 *
 * <p>The constant classes in this package provide centralized definitions for commonly used values
 * throughout the application:
 *
 * <ul>
 *   <li>{@link com.enttribe.emailagent.constant.APIConstants} - Defines API-related constants
 *       including:
 *       <ul>
 *         <li>API role definitions for authorization and security
 *         <li>Default configuration values
 *         <li>API endpoint permission constants
 *       </ul>
 *   <li>{@link com.enttribe.emailagent.constant.EmailConstants} - Defines email and
 *       calendar-related constants including:
 *       <ul>
 *         <li>Date and time format patterns
 *         <li>Meeting and event field names
 *         <li>HTTP header constants
 *         <li>JSON field names and values
 *         <li>Status and result constants
 *       </ul>
 * </ul>
 *
 * <p>These constant classes are designed as utility classes with private constructors to prevent
 * instantiation. They provide a centralized location for all application constants, making the code
 * more maintainable and reducing the risk of typos in string literals throughout the application.
 *
 * <p>The constants are used across all layers of the application including REST controllers,
 * service classes, utility methods, and data transfer objects to ensure consistency in naming
 * conventions and configuration values.
 *
 * <AUTHOR>
 * @version 1.0.7
 * @since 1.0.0
 */
package com.enttribe.emailagent.constant;
