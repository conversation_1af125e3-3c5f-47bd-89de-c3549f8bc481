/*
 * Copyright © 2023–2025 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of VisionWave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of VisionWave.
 */

/**
 * This package contains utility classes that provide common functionality and helper methods for
 * the Email Agent application.
 *
 * <p>The utilities in this package support various operations including:
 *
 * <ul>
 *   <li>{@link com.enttribe.emailagent.utils.CommonUtils} - General utility methods for meeting
 *       request conversion, JSON manipulation, and common operations
 *   <li>{@link com.enttribe.emailagent.utils.DateUtils} - Date and time manipulation utilities
 *       including timezone conversions and date formatting
 *   <li>{@link com.enttribe.emailagent.utils.EmailUtils} - Email-specific utility methods for email
 *       validation and processing
 *   <li>{@link com.enttribe.emailagent.utils.JsonUtils} - JSON processing utilities for parsing,
 *       formatting, and manipulation of JSON data
 *   <li>{@link com.enttribe.emailagent.utils.TokenUtils} - Token management utilities for handling
 *       authentication tokens and access tokens
 *   <li>{@link com.enttribe.emailagent.utils.WorkingHoursUtil} - Working hours calculation and time
 *       slot management utilities
 * </ul>
 *
 * <p>All utility classes in this package are designed to be stateless and provide static methods
 * for common operations. They support the core business logic by providing reusable functionality
 * across the application.
 *
 * <AUTHOR>
 * @version 1.0.7
 * @since 1.0.0
 */
package com.enttribe.emailagent.utils;
