package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.exception.BusinessException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * A utility class to handle JSON to Java object conversion and vice versa. Provides methods to
 * serialize and deserialize JSON using a singleton ObjectMapper instance.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public final class JsonUtils {

  private JsonUtils() {
    throw new UnsupportedOperationException("utility class can not be instantiated");
  }

  // Single, static, and final ObjectMapper instance
  private static final ObjectMapper OBJECT_MAPPER = createObjectMapper();

  /**
   * Creates and configures an ObjectMapper instance.
   *
   * @return the configured ObjectMapper
   */
  private static ObjectMapper createObjectMapper() {
    ObjectMapper mapper = new ObjectMapper();
    mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    mapper.registerModule(new JavaTimeModule());
    return mapper;
  }

  /**
   * Provides a singleton instance of ObjectMapper to be reused.
   *
   * @return the ObjectMapper instance
   */
  public static ObjectMapper getObjectMapper() {
    return OBJECT_MAPPER;
  }

  /**
   * Converts a Java object to a valid JSON string.
   *
   * @param object the Java object to convert to JSON
   * @return the JSON string representation of the object
   */
  public static String convertToJSON(Object object) {
    try {
      ObjectMapper mapper = getObjectMapper();
      return mapper.writeValueAsString(object);
    } catch (Exception e) {
      log.error("error inside @convertToJSON : {}", e.getMessage());
      throw new BusinessException(
          "Error converting object to JSON in convertToJSON method. Object: "
              + object
              + " , Exception message: "
              + e.getMessage());
    }
  }

  /**
   * Converts a JSON string to an object of type T.
   *
   * @param <T> the type parameter for the object
   * @param jsonString the JSON string to convert
   * @param clazz the class of type T
   * @return the object of type T
   */
  public static <T> T convertJsonToObject(String jsonString, Class<T> clazz) {
    ObjectMapper objectMapper = getObjectMapper();
    try {
      return objectMapper.readValue(jsonString, clazz);
    } catch (Exception e) {
      log.error("error inside @convertJsonToObject : {}", e.getMessage());
      throw new BusinessException(
          "Error converting JSON string to object in convertJsonToObject method. JSON:"
              + jsonString
              + "Target class: "
              + clazz.getName()
              + "Exception message: "
              + e.getMessage());
    }
  }

  /**
   * Converts a JSON list string to a List of type T.
   *
   * @param <T> the type of the List elements
   * @param jsonString the JSON list string to convert
   * @param clazz the class of type T
   * @return the List of type T
   * @throws JsonProcessingException if an error occurs during JSON processing
   */
  public static <T> List<T> convertJsonToList(String jsonString, Class<T> clazz)
      throws JsonProcessingException {
    ObjectMapper objectMapper = getObjectMapper();
    try {
      if (!StringUtils.hasText(jsonString)) return List.of();
      return objectMapper.readValue(
          jsonString, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
    } catch (Exception e) {
      log.error("error inside @convertJsonToList : {}", e.getMessage());
      throw new BusinessException(
          "Error converting JSON string to list in convertJsonToList method. JSON: "
              + jsonString
              + "Target class: "
              + clazz.getName()
              + "Exception message: "
              + e.getMessage());
    }
  }

  public static JsonNode toJsonNode(String jsonString) {
    try {
      return getObjectMapper().readTree(jsonString);
    } catch (JsonProcessingException e) {
      log.error("error inside @toJsonNode : {}", e.getMessage());
      throw new BusinessException("Failed to parse JSON string into JsonNode", e);
    }
  }
}
