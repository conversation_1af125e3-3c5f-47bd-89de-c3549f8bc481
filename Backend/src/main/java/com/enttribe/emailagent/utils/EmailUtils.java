package com.enttribe.emailagent.utils;

import java.util.List;

public class EmailUtils {

  private EmailUtils() {
    throw new UnsupportedOperationException("utility class can not be instantiated");
  }

  public static boolean checkDomain(String email, List<String> domainList) {
    if (email == null || !email.contains("@")) {
      return false;
    }

    String domain = email.substring(email.indexOf("@") + 1).toLowerCase();
    return domainList.stream().map(String::toLowerCase).anyMatch(allowed -> allowed.equals(domain));
  }
}
