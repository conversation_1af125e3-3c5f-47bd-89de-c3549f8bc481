package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.dto.Meeting;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class EventUtils {

    public static List<Meeting> findAvailableTimeSlots(Map<String, List<Meeting>> meetings, Date from, Date till, long meetingDurationMillis) {
        List<Meeting> availableSlots = new ArrayList<>();
        // Merge all meetings into one list and sort by start time
        List<Meeting> allMeetings = new ArrayList<>();
        for (List<Meeting> userMeetings : meetings.values()) {
            allMeetings.addAll(userMeetings);
        }
        allMeetings.sort(Comparator.comparing(Meeting::getStartTime));
        // Initialize the currentStart to the 'from' date
        Date currentStart = from;
        for (Meeting meeting : allMeetings) {
            // Check if the gap between currentStart and the next meeting's startTime is enough
            if (meeting.getStartTime().after(currentStart)) {
                long gap = meeting.getStartTime().getTime() - currentStart.getTime();
                while (gap >= meetingDurationMillis) {
                    Date potentialEnd = new Date(currentStart.getTime() + meetingDurationMillis);

                    // Check if currentStart or potentialEnd goes beyond 'till'
                    if (currentStart.after(till) || potentialEnd.after(till)) {
                        break; // Exit if it exceeds 'till'
                    }
                    // Add the slot if valid
                    availableSlots.add(new Meeting(currentStart, potentialEnd));

                    // Move to the next slot
                    currentStart = potentialEnd;

                    // Recalculate the gap for the next iteration
                    gap = meeting.getStartTime().getTime() - currentStart.getTime();
                }
            }

            // Move currentStart to the end of the current meeting if it's later
            if (meeting.getEndTime().after(currentStart)) {
                currentStart = meeting.getEndTime();
            }
        }
        // Check the time slot after the last meeting until 'till' time
        while (currentStart.before(till) && (till.getTime() - currentStart.getTime() >= meetingDurationMillis)) {
            Date potentialEnd = new Date(currentStart.getTime() + meetingDurationMillis);
            if (potentialEnd.after(till)) {
                break;
            }
            availableSlots.add(new Meeting(currentStart, potentialEnd));
            currentStart = potentialEnd;
        }

        return availableSlots;
    }

}
