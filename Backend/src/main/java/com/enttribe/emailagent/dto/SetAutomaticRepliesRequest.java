package com.enttribe.emailagent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for setting automatic replies settings via Microsoft Graph API.
 * This class represents the request payload for updating automatic replies configuration.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SetAutomaticRepliesRequest {

    /**
     * The status of automatic replies. Possible values are: disabled, alwaysEnabled, scheduled.
     */
    private String status;

    /**
     * The automatic reply message that is sent to external senders.
     */
    @JsonProperty("externalReplyMessage")
    private String externalReplyMessage;

    /**
     * The automatic reply message that is sent to internal senders.
     */
    @JsonProperty("internalReplyMessage")
    private String internalReplyMessage;

    /**
     * The date and time when automatic replies are scheduled to start.
     */
    @JsonProperty("scheduledStartDateTime")
    private DateTimeTimeZone scheduledStartDateTime;

    /**
     * The date and time when automatic replies are scheduled to end.
     */
    @JsonProperty("scheduledEndDateTime")
    private DateTimeTimeZone scheduledEndDateTime;

    /**
     * Nested class for date and time with timezone information.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DateTimeTimeZone {
        /**
         * The date and time in ISO 8601 format.
         */
        private String dateTime;

        /**
         * The timezone identifier.
         */
        private String timeZone;
    }
}
