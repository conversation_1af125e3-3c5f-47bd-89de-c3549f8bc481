package com.enttribe.emailagent.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AvailabilityStatus {

  private String email;
  private String status; // "free", "tentative", "busy", "outOfOffice", "workingElsewhere"
  private Object organizerMeeting; // "free", "tentative", "busy", "outOfOffice", "workingElsewhere"
}
