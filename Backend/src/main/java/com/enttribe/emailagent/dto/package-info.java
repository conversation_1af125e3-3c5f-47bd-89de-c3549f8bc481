/*
 * Copyright © 2023–2025 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of VisionWave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of VisionWave.
 */

/**
 * This package contains Data Transfer Objects (DTOs) that are used for transferring data between
 * different layers of the Email Agent application.
 *
 * <p>The DTOs in this package represent various data structures including:
 *
 * <ul>
 *   <li>{@link com.enttribe.emailagent.dto.EventDto} - Represents calendar event data including
 *       event details, attendees, and scheduling information
 *   <li>{@link com.enttribe.emailagent.dto.Meeting} - Represents meeting information including
 *       meeting details, time slots, and participant data
 *   <li>{@link com.enttribe.emailagent.dto.AvailableSlots} - Represents available time slots for
 *       scheduling meetings and appointments
 *   <li>{@link com.enttribe.emailagent.dto.AvailabilityResponse} - Represents availability
 *       information for multiple users and time slots
 *   <li>{@link com.enttribe.emailagent.dto.AvailabilityStatus} - Represents the status of user
 *       availability for specific time periods
 *   <li>{@link com.enttribe.emailagent.dto.AttendeeAndStatus} - Represents attendee information
 *       along with their response status for meetings
 *   <li>{@link com.enttribe.emailagent.dto.TimeSlot} - Represents time slot information for
 *       scheduling and availability checking
 *   <li>{@link com.enttribe.emailagent.dto.WorkingHoursSummary} - Represents working hours
 *       information including time ranges and availability periods
 *   <li>{@link com.enttribe.emailagent.dto.GraphUserDto} - Represents Microsoft Graph user
 *       information and profile data
 *   <li>{@link com.enttribe.emailagent.dto.ForwardEventDto} - Represents event forwarding
 *       information including recipients and comments
 *   <li>{@link com.enttribe.emailagent.dto.OutOfOffice} - Represents out-of-office settings and
 *       automatic reply configurations
 *   <li>{@link com.enttribe.emailagent.dto.ZonedMeeting} - Represents meeting information with
 *       timezone-specific details
 *   <li>{@link com.enttribe.emailagent.dto.MeetingWrapper} - Wrapper for meeting information with
 *       additional metadata
 *   <li>{@link com.enttribe.emailagent.dto.ConflictMeetingWrapper} - Represents meeting conflicts
 *       and scheduling issues
 * </ul>
 *
 * <p>These DTOs are designed to facilitate clean data transfer between the REST API layer, service
 * layer, and external integrations. They provide a standardized way to represent complex data
 * structures and ensure type safety across the application.
 *
 * <AUTHOR>
 * @version 1.0.7
 * @since 1.0.0
 */
package com.enttribe.emailagent.dto;
