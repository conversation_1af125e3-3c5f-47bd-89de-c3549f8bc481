package com.enttribe.emailagent.exception;

import com.enttribe.emailagent.constant.EmailConstants;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

/**
 * The type Global exception handler.
 *
 * <AUTHOR> Sonsale
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

  @ExceptionHandler(ResourceNotFoundException.class)
  public ResponseEntity<ErrorDetails> handlerResourceNotFoundException(
      ResourceNotFoundException ex, WebRequest request) {
    ErrorDetails errorDetails =
        new ErrorDetails(new Date(), ex.getMessage(), request.getDescription(false));
    return new ResponseEntity<>(errorDetails, HttpStatus.OK);
  }

  @ExceptionHandler(BusinessException.class)
  public ResponseEntity<ErrorDetails> handlerBusinessException(
      BusinessException ex, WebRequest request) {
    ErrorDetails errorDetails =
        new ErrorDetails(new Date(), ex.getMessage(), request.getDescription(false));
    return new ResponseEntity<>(errorDetails, HttpStatus.OK);
  }

  @ExceptionHandler(ApiException.class)
  public ResponseEntity<Map<String, Object>> handlerApiException(ApiException ex) {
    Map<String, Object> errorMap = new HashMap<>();
    errorMap.put(EmailConstants.ERROR, "Something went wrong!");
    errorMap.put(EmailConstants.MESSAGE, ex.getMessage());
    return new ResponseEntity<>(errorMap, HttpStatus.OK);
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<Map<String, Object>> handlerGlobalException(Exception ex) {
    Map<String, Object> errorMap = new HashMap<>();
    errorMap.put(EmailConstants.ERROR, "Something went wrong!");
    errorMap.put(EmailConstants.MESSAGE, ex.getMessage());
    return new ResponseEntity<>(errorMap, HttpStatus.OK);
  }
}
