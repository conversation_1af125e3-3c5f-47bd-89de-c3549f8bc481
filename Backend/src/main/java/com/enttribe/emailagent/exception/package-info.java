/*
 * Copyright © 2023–2025 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of VisionWave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of VisionWave.
 */

/**
 * This package contains custom exception classes and error handling components for the Email Agent
 * application.
 *
 * <p>The exception classes in this package provide structured error handling and include:
 *
 * <ul>
 *   <li>{@link com.enttribe.emailagent.exception.ApiException} - Represents API-level exceptions
 *       that occur during HTTP request processing
 *   <li>{@link com.enttribe.emailagent.exception.BusinessException} - Represents business logic
 *       exceptions that occur during application operations
 *   <li>{@link com.enttribe.emailagent.exception.ResourceNotFoundException} - Represents exceptions
 *       when requested resources are not found
 *   <li>{@link com.enttribe.emailagent.exception.ErrorDetails} - Represents structured error
 *       information including error codes, messages, and timestamps
 *   <li>{@link com.enttribe.emailagent.exception.GlobalExceptionHandler} - Provides centralized
 *       exception handling for the entire application, converting exceptions to appropriate HTTP
 *       responses
 * </ul>
 *
 * <p>These exception classes follow a hierarchical structure to provide different levels of error
 * handling. The global exception handler ensures consistent error responses across all API
 * endpoints and provides proper HTTP status codes and error messages.
 *
 * <p>All exceptions include proper error details and are designed to provide meaningful information
 * for debugging and user feedback while maintaining security by not exposing sensitive internal
 * information.
 *
 * <AUTHOR>
 * @version 1.0.7
 * @since 1.0.0
 */
package com.enttribe.emailagent.exception;
