/*
 * Copyright © 2023–2025 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of VisionWave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of VisionWave.
 *
 * Data Access Object (DAO) package for the Email Agent application.
 *
 * <p>This package contains repository interfaces that provide data access functionality
 * for the Email Agent domain entities. These DAOs extend Spring Data JPA repositories
 * to provide CRUD operations and custom queries for email-related data.</p>
 *
 * <h2>Package Contents:</h2>
 * <ul>
 *   <li><strong>EmailUserDao</strong> - Repository for managing email user entities</li>
 *   <li><strong>EmailPreferencesDao</strong> - Repository for managing user email preferences</li>
 * </ul>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Spring Data JPA integration for automatic CRUD operations</li>
 *   <li>Custom JPQL queries for specific business requirements</li>
 *   <li>Repository pattern implementation for clean data access</li>
 *   <li>Support for email user management and preferences</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>These DAOs are typically injected into service layer classes to provide
 * data access capabilities. They handle database operations for:</p>
 * <ul>
 *   <li>Email user registration and management</li>
 *   <li>User preference storage and retrieval</li>
 *   <li>Email-related configuration data</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.emailagent.dao;
