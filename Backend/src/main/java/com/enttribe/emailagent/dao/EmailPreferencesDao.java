package com.enttribe.emailagent.dao;

import com.enttribe.emailagent.entity.EmailPreferences;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface EmailPreferencesDao extends JpaRepository<EmailPreferences, Integer> {

  @Query("select u from EmailPreferences u where u.userId = ?1 ")
  EmailPreferences getEmailPreferencesByUserId(String userId);
}
