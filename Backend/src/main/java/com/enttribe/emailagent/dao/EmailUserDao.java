package com.enttribe.emailagent.dao;

import com.enttribe.emailagent.entity.EmailUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EmailUserDao extends JpaRepository<EmailUser, Integer> {

  @Query("SELECT eu FROM EmailUser eu WHERE eu.email = :email")
  EmailUser findByEmail(String email);

  @Query(
      "SELECT CASE WHEN COUNT(e) > 0 THEN true ELSE false END FROM EmailUser e WHERE e.email = :email AND e.deleted = false")
  boolean existsByEmailAndNotDeleted(@Param("email") String email);
}
