/*
 * Copyright © 2023–2025 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of VisionWave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of VisionWave.
 */

/**
 * This package contains service classes that implement the business logic for the Email Agent
 * application.
 *
 * <p>The services in this package handle core application functionality including Microsoft Graph
 * API integration, calendar management, and email processing.
 *
 * <p>Key services include:
 *
 * <ul>
 *   <li>{@link com.enttribe.emailagent.service.graph.GraphIntegrationService} - Provides comprehensive
 *       integration with Microsoft Graph API for calendar operations including:
 *       <ul>
 *         <li>Calendar event management (create, update, delete, forward)
 *         <li>Meeting scheduling and availability checking
 *         <li>Event response handling (accept, decline, tentative)
 *         <li>Working hours and time slot calculations
 *         <li>User availability and conflict detection
 *       </ul>
 * </ul>
 *
 * <p>Services in this package follow Spring's service layer pattern and are designed to be
 * stateless and thread-safe. They coordinate between data access objects (DAOs) and REST
 * controllers to provide a complete business logic layer.
 *
 * <p>All services include proper error handling, logging, and integration with the application's
 * authentication and user context management.
 *
 * <AUTHOR>
 * @version 1.0.7
 * @since 1.0.0
 */
package com.enttribe.emailagent.service;
