package com.enttribe.emailagent.service.ews;

import com.enttribe.emailagent.constant.EmailConstants;
import com.enttribe.emailagent.dao.EmailPreferencesDao;
import com.enttribe.emailagent.dao.EmailUserDao;
import com.enttribe.emailagent.dto.AvailabilityResponse;
import com.enttribe.emailagent.dto.AvailabilityStatus;
import com.enttribe.emailagent.dto.AvailableSlots;
import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.dto.GraphUserDto;
import com.enttribe.emailagent.dto.Meeting;
import com.enttribe.emailagent.dto.SetAutomaticRepliesRequest;
import com.enttribe.emailagent.dto.TimeSlot;
import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.service.EventService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.ConverterUtils;
import com.enttribe.emailagent.utils.DateUtils;
import com.enttribe.emailagent.utils.EWSUtils;
import com.enttribe.emailagent.utils.EmailUtils;
import com.enttribe.emailagent.utils.EventUtils;
import com.enttribe.emailagent.utils.JsonUtils;
import com.enttribe.emailagent.utils.ZoomClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.PropertySet;
import microsoft.exchange.webservices.data.core.enumeration.availability.AvailabilityData;
import microsoft.exchange.webservices.data.core.enumeration.availability.FreeBusyViewType;
import microsoft.exchange.webservices.data.core.enumeration.availability.MeetingAttendeeType;
import microsoft.exchange.webservices.data.core.enumeration.property.OofState;
import microsoft.exchange.webservices.data.core.enumeration.property.WellKnownFolderName;
import microsoft.exchange.webservices.data.core.enumeration.property.time.DayOfTheWeek;
import microsoft.exchange.webservices.data.core.enumeration.property.time.Month;
import microsoft.exchange.webservices.data.core.enumeration.service.ConflictResolutionMode;
import microsoft.exchange.webservices.data.core.enumeration.service.SendInvitationsMode;
import microsoft.exchange.webservices.data.core.enumeration.service.SendInvitationsOrCancellationsMode;
import microsoft.exchange.webservices.data.core.response.AttendeeAvailability;
import microsoft.exchange.webservices.data.core.service.folder.CalendarFolder;
import microsoft.exchange.webservices.data.core.service.item.Appointment;
import microsoft.exchange.webservices.data.misc.availability.AttendeeInfo;
import microsoft.exchange.webservices.data.misc.availability.AvailabilityOptions;
import microsoft.exchange.webservices.data.misc.availability.GetUserAvailabilityResults;
import microsoft.exchange.webservices.data.misc.availability.OofReply;
import microsoft.exchange.webservices.data.misc.availability.TimeWindow;
import microsoft.exchange.webservices.data.property.complex.Attendee;
import microsoft.exchange.webservices.data.property.complex.ItemId;
import microsoft.exchange.webservices.data.property.complex.MessageBody;
import microsoft.exchange.webservices.data.property.complex.availability.CalendarEvent;
import microsoft.exchange.webservices.data.property.complex.availability.OofSettings;
import microsoft.exchange.webservices.data.property.complex.availability.WorkingHours;
import microsoft.exchange.webservices.data.property.complex.recurrence.pattern.Recurrence;
import microsoft.exchange.webservices.data.search.CalendarView;
import microsoft.exchange.webservices.data.search.FindItemsResults;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;

import static com.enttribe.emailagent.utils.CommonUtils.convertStringToDate;

@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "email.platform", havingValue = "ews")
public class EwsEventService implements EventService {


    @Value("${org.domain.name}")
    private List<String> orgDomainName;

    @Value("${outsiders.events.lookup.allow}")
    private Boolean allowOutsiders;
    private final EmailUserDao emailUserDao;
    private final EmailPreferencesDao preferencesDao;
    private final UserContextHolder userContextHolder;

    /**
     * Gets calendar events v1.
     * @param email         the email
     * @param startDateTime the start date time
     * @param endDateTime   the end date time
     * @return the calendar events v 1
     */
    @Override
    public List<EventDto> getCalendarEventsV1(String email, String startDateTime, String endDateTime, String subject) {
        return getCalendarEvents(email, startDateTime, endDateTime, false);
    }

    @Override
    public Map<String, Object> updateEvent(String eventId, String email, String meetingRequestJson) {
        log.debug("Inside @method updateEvent. @param: email -> {}, eventId -> {}, jsonBody -> {}", email, eventId, meetingRequestJson);

        try {
            // Parse the JSON request to extract event details
            JSONObject requestJson = new JSONObject(meetingRequestJson);

            // Get EWS service for the user
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // For EWS, we need to find the appointment by searching for it
            // since the eventId might be a Graph API ID, not an EWS ItemId
            Appointment appointment = findAppointmentByEventId(service, eventId, email);
            if (appointment == null) {
                log.error("Appointment not found for eventId: {}", eventId);
                return Map.of(EmailConstants.RESULT, EmailConstants.FAILED, "error", "Appointment not found");
            }

            appointment.load(PropertySet.FirstClassProperties);

            // Update appointment properties from the JSON request
            // Only update properties that are safe to modify in EWS

            if (requestJson.has("subject")) {
                try {
                    appointment.setSubject(requestJson.getString("subject"));
                    log.debug("Updated subject successfully");
                } catch (Exception e) {
                    log.warn("Error setting subject: {}", e.getMessage());
                }
            }

            if (requestJson.has("body")) {
                try {
                    Object bodyObj = requestJson.get("body");
                    String bodyContent;
                    if (bodyObj instanceof JSONObject) {
                        JSONObject bodyJson = (JSONObject) bodyObj;
                        bodyContent = bodyJson.getString("content");
                    } else if (bodyObj instanceof String) {
                        bodyContent = (String) bodyObj;
                    } else {
                        bodyContent = bodyObj.toString();
                    }
                    appointment.setBody(MessageBody.getMessageBodyFromText(bodyContent));
                    log.debug("Updated body successfully");
                } catch (Exception e) {
                    log.warn("Error parsing body content: {}", e.getMessage());
                }
            }

            if (requestJson.has("location")) {
                try {
                    Object locationObj = requestJson.get("location");
                    String location;
                    if (locationObj instanceof JSONObject) {
                        JSONObject locationJson = (JSONObject) locationObj;
                        location = locationJson.getString("displayName");
                    } else {
                        location = locationObj.toString();
                    }
                    appointment.setLocation(location);
                    log.debug("Updated location successfully");
                } catch (Exception e) {
                    log.warn("Error parsing location: {}", e.getMessage());
                }
            }

            // Note: Start and end times are often read-only in EWS for existing appointments
            // We'll skip updating them to avoid the "Set action is invalid for property" error
            if (requestJson.has("start") && requestJson.has("end")) {
                log.info("Skipping start/end time updates as they may be read-only in EWS");
            }

            // Update attendees if provided (be conservative as attendees can be read-only)
            if (requestJson.has("attendees")) {
                log.info("Skipping attendee updates as they may be read-only in EWS");
                // Note: Attendee updates often fail in EWS with "Set action is invalid for property"
                // This is a known limitation of EWS for existing appointments
            }

            // Save the updated appointment with fallback mechanisms
            try {
                appointment.update(ConflictResolutionMode.AlwaysOverwrite, SendInvitationsOrCancellationsMode.SendToAllAndSaveCopy);
                log.info("Appointment updated successfully with SendToAllAndSaveCopy");
            } catch (Exception e) {
                log.warn("Failed to update with SendToAllAndSaveCopy, trying SendToChangedAndSaveCopy: {}", e.getMessage());
                try {
                    appointment.update(ConflictResolutionMode.AlwaysOverwrite, SendInvitationsOrCancellationsMode.SendToChangedAndSaveCopy);
                    log.info("Appointment updated successfully with SendToChangedAndSaveCopy");
                } catch (Exception e2) {
                    log.warn("Failed to update with SendToChangedAndSaveCopy, trying SendOnlyToAll: {}", e2.getMessage());
                    try {
                        appointment.update(ConflictResolutionMode.AlwaysOverwrite, SendInvitationsOrCancellationsMode.SendOnlyToAll);
                        log.info("Appointment updated successfully with SendOnlyToAll");
                    } catch (Exception e3) {
                        log.error("All update methods failed: {}", e3.getMessage());
                        throw e3; // Re-throw the last exception
                    }
                }
            }

            // Convert the updated appointment to EventDto
            EventDto eventDto = ConverterUtils.convertToEventDto(appointment);

            Map<String, Object> result = new HashMap<>();
            result.put(EmailConstants.RESULT, EmailConstants.SUCCESS);
            result.put("updatedEventObject", new JSONObject(eventDto).toString());

            log.debug("Event updated successfully: {}", eventId);
            return result;

        } catch (Exception e) {
            log.error("Exception while updating event: {}", e.getMessage(), e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED, "error", e.getMessage());
        }
    }


    @Override
    public Map<String, String> declineMeeting(String meetingRequestId) {
        try {
            log.debug("Inside @method declineMeeting. @param : meetingRequestId -> {}", meetingRequestId);
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the meeting request by its ID
            ItemId itemId = new ItemId(meetingRequestId);
            Appointment appointment = Appointment.bind(service, itemId);
            // Load appointment properties
            appointment.load();
            // Accept the meeting request
            appointment.decline(true); // true for sending response to all attendees, false for not sending

            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("meetingRequestId", meetingRequestId);
            log.error("Error inside @method declineMeeting : {} ", e.getMessage(), e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    @Override
    public Map<String, String> acceptMeeting(String meetingRequestId) {
        try {
            log.debug("Inside @method acceptMeeting. @param : meetingRequestId -> {}", meetingRequestId);
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the meeting request by its ID
            ItemId itemId = new ItemId(meetingRequestId);
            Appointment appointment = Appointment.bind(service, itemId);
            // Load appointment properties
            appointment.load();
            // Accept the meeting request
            appointment.accept(true);
            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("meetingRequestId", meetingRequestId);
            log.error("Error inside @method acceptMeeting : {} ", e.getMessage(), e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    @Override
    public Map<String, String> tentativelyAccept(String meetingRequestId) {
        try {
            log.debug("Inside @method tentativelyAcceptMeeting. @param : meetingRequestId -> {}", meetingRequestId);
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the meeting request by its ID
            ItemId itemId = new ItemId(meetingRequestId);
            Appointment appointment = Appointment.bind(service, itemId);
            // Load appointment properties
            appointment.load();
            // Tentatively accept the meeting request
            appointment.acceptTentatively(true); // true for sending response to all attendees, false for not sending

            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("meetingRequestId", meetingRequestId);
            log.error("Error inside @method tentativelyAcceptMeeting : {} ", e.getMessage(), e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    @Override
    public Map<String, Object> scheduleEvent(String email, Map<String, Object> requestBody) {
        try {
            String subject = (String) requestBody.get("subject");
            String body = (String) requestBody.get("body");
            String meetingStartTime = (String) requestBody.get("meetingStartTime");
            String meetingEndTime = (String) requestBody.get("meetingEndTime");
            List<String> attendees = (List<String>) requestBody.get("requiredAttendees");
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            Appointment appointment = new Appointment(service);

            // Set appointment properties
            appointment.setSubject(subject);
            appointment.setBody(MessageBody.getMessageBodyFromText(body));

            Date startDate, endDate;
            if (meetingStartTime.endsWith("Z")) {
                startDate = DateUtils.parseDate(meetingStartTime);
                endDate = DateUtils.parseDate(meetingEndTime);
            } else {
                String timeZone = (String) requestBody.get(EmailConstants.TIME_ZONE);
                startDate = DateUtils.parseDateWithTimeZone(meetingStartTime, timeZone);
                endDate = DateUtils.parseDateWithTimeZone(meetingEndTime, timeZone);
            }
            appointment.setStart(startDate);
            appointment.setEnd(endDate);
            String location = Optional.ofNullable((String) requestBody.get(EmailConstants.LOCATION)).orElse(null);
            String locationUrl = Optional.ofNullable((String) requestBody.get(EmailConstants.LOCATION_URL)).orElse(null);
            log.info("location is {} and locationUrl is {}", location, locationUrl);
            //            appointment.setLocation("Conference Room");

            // Add required attendees
            attendees.forEach(attendee -> {
                try {
                    appointment.getRequiredAttendees().add(new Attendee(attendee));
                } catch (Exception e) {
                    log.error("Error while adding attendees for scheduleMeeting", e);
                }
            });
            String meetingType = (String) requestBody.get("meetingType");
            String timeZone = (String) requestBody.get(EmailConstants.TIME_ZONE);

            if (meetingType != null && meetingType.equalsIgnoreCase("Zoom")) {
                try {
                    Map<String, String> zoomResponse = ZoomClient.scheduleMeeting(email, requestBody.get(EmailConstants.MEETING_START_TIME).toString(), requestBody.get(EmailConstants.MEETING_END_TIME).toString(), requestBody.get("subject").toString(), requestBody.get("body").toString(), timeZone);

                    if (zoomResponse != null) {
                        String zoomBody = requestBody.get("body").toString();
                        body += "<div>Meeting URL : <a style=\"color: blue\" href=" + zoomResponse.get("join_url") + ">" + zoomResponse.get("join_url") + "</a></div><br>Meeting ID : " + zoomResponse.get("meeting_id") + "<br>Passcode : " + zoomResponse.get("passcode");
                        log.debug("body is {}", body);
                        appointment.setBody(MessageBody.getMessageBodyFromText(body));
                    }
                } catch (Exception e) {
                    log.error("Error setting Zoom meeting url :{}", e.getMessage(), e);
                }
            }

            // Recurrence information

            boolean isRecurring = requestBody.get("recurrence") != null;

            // Set recurrence if it's a recurring meeting
            if (isRecurring) {
                log.debug("Recurrence information found: {}", requestBody.get("recurrence"));
                JSONObject recurrenceJson = new JSONObject((Map<String, Object>) requestBody.get("recurrence"));
                JSONObject pattern = recurrenceJson.getJSONObject("pattern");
                JSONObject range = recurrenceJson.getJSONObject("range");
                String recurrenceType = pattern.optString("type"); // Daily, Weekly, AbsoluteMonthly
                String rangeType =  range.optString("type"); // "EndDate", "NoEnd"
                int interval =  pattern.optInt("interval"); // Interval for recurrence
                String recurrenceStartDate =  range.optString("startDate");
                String recurrenceEndDate =  range.optString("endDate");
                int dayOfMonth =  pattern.optInt("dayOfMonth");
                int monthOfYear =  pattern.optInt("monthOfYear");
                JSONArray daysArray = pattern.getJSONArray("daysOfWeek");

                List<String> daysOfWeek = new ArrayList<>();
                for (int i = 0; i < daysArray.length(); i++) {
                    daysOfWeek.add(daysArray.getString(i));
                }

                // Parse recurrence start date
                SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
                Date recStartDate = dateFormatter.parse(recurrenceStartDate);

                // Define recurrence pattern based on the recurrenceType
                if ("Weekly".equalsIgnoreCase(recurrenceType)) {
                    DayOfTheWeek[] dayOfTheWeekArray = daysOfWeek.stream()
                            .map(EwsEventService::toPascalCase)
                            .map(DayOfTheWeek::valueOf)
                            .toArray(DayOfTheWeek[]::new);

                    Recurrence.WeeklyPattern weeklyPattern = new Recurrence.WeeklyPattern(recStartDate, interval, dayOfTheWeekArray);

                    if ("EndDate".equalsIgnoreCase(rangeType)) {
                        Date recEndDate = dateFormatter.parse(recurrenceEndDate);
                        weeklyPattern.setEndDate(recEndDate);
                    } else if ("NoEnd".equalsIgnoreCase(rangeType)) {
                        weeklyPattern.neverEnds();
                    }

                    appointment.setRecurrence(weeklyPattern);

                } else if ("Daily".equalsIgnoreCase(recurrenceType)) {
                    Recurrence.DailyPattern dailyPattern = new Recurrence.DailyPattern(recStartDate, interval);

                    if ("EndDate".equalsIgnoreCase(rangeType)) {
                        Date recEndDate = dateFormatter.parse(recurrenceEndDate);
                        dailyPattern.setEndDate(recEndDate);
                    } else if ("NoEnd".equalsIgnoreCase(rangeType)) {
                        dailyPattern.neverEnds();
                    }

                    appointment.setRecurrence(dailyPattern);

                } else if ("AbsoluteMonthly".equalsIgnoreCase(recurrenceType)) {
                    Recurrence.MonthlyPattern monthlyPattern = new Recurrence.MonthlyPattern(recStartDate, interval, dayOfMonth);

                    if ("EndDate".equalsIgnoreCase(rangeType)) {
                        Date recEndDate = dateFormatter.parse(recurrenceEndDate);
                        monthlyPattern.setEndDate(recEndDate);
                    } else if ("NoEnd".equalsIgnoreCase(rangeType)) {
                        monthlyPattern.neverEnds();
                    }

                    appointment.setRecurrence(monthlyPattern);
                } else if ("absoluteYearly".equalsIgnoreCase(recurrenceType)) {
                    // Handle yearly recurrence
                    Month monthEnum = Month.values()[monthOfYear - 1];
                    Recurrence.YearlyPattern yearlyPattern = new Recurrence.YearlyPattern(recStartDate, monthEnum, dayOfMonth);

                    if ("EndDate".equalsIgnoreCase(rangeType)) {
                        Date recEndDate = dateFormatter.parse(recurrenceEndDate);
                        yearlyPattern.setEndDate(recEndDate);
                    } else if ("NoEnd".equalsIgnoreCase(rangeType)) {
                        yearlyPattern.neverEnds();
                    }

                    appointment.setRecurrence(yearlyPattern);
                }
                // Additional recurrence types (e.g., yearly) can be added similarly.
            }

            setOptionalAttendees(appointment, requestBody);
            appointment.save(WellKnownFolderName.Calendar, SendInvitationsMode.SendToAllAndSaveCopy);
            log.debug("Meeting scheduled successfully.");
            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } catch (Exception e) {
            log.error("Error inside @method scheduleEvent : {} ", e.getMessage(), e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    public static String toPascalCase(String input) {
        if (input == null || input.isEmpty()) {
            return input; // return as is
        }
        input = input.trim(); // remove leading/trailing spaces
        return input.substring(0, 1).toUpperCase() + input.substring(1).toLowerCase();
    }

    private void setOptionalAttendees(Appointment appointment, Map<String, Object> requestBody) {
        if (requestBody != null && requestBody.containsKey("optionalAttendees")) {
            List<String> optionals = (List<String>) requestBody.get("optionalAttendees");
            optionals.forEach(attendee -> {
                try {
                    appointment.getOptionalAttendees().add(new Attendee(attendee));
                } catch (Exception e) {
                    log.error("Error while adding attendees for scheduleMeeting", e);
                }
            });
        }
    }

    @Override
    public Map<String, String> rescheduleEvent(Map<String, String> requestBody) {
        log.debug("Inside @method rescheduleEvent. @param : requestMap -> {}", requestBody);
        String email = userContextHolder.getCurrentUser().getEmail();
        try {
            // Get the ExchangeService object for the user
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            String eventId = requestBody.get("eventId");
            // Bind to the appointment using the appointmentId
            ItemId itemId = new ItemId(eventId);
            Appointment appointment = Appointment.bind(service, itemId);

            // Update the appointment's start and end time
            String startTime = requestBody.get("startTime");
            String endTime = requestBody.get("endTime");
            String timeZone = requestBody.get("timeZone");
            if (timeZone == null) {
                EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
                timeZone = preferences.getTimeZone();
            }

            Date startDate = DateUtils.parseDateWithTimeZone(startTime, timeZone);
            Date endDate = DateUtils.parseDateWithTimeZone(endTime, timeZone);
            appointment.setStart(startDate);
            appointment.setEnd(endDate);

            // Add description or reason for rescheduling
            String description = requestBody.get("rescheduleReason");
            if (description != null && !description.isEmpty()) {
                appointment.setBody(MessageBody.getMessageBodyFromText(description));
            }
            // Save the changes to the appointment
            appointment.update(ConflictResolutionMode.AutoResolve, SendInvitationsOrCancellationsMode.SendToAllAndSaveCopy);
            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } catch (Exception e) {
            log.error("Error while rescheduling event : {} ", e.getMessage(), e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    @Override
    public List<EventDto> getEventDetailsBySubjectAndTime(String email, String subject, String startDateTime, String endDateTime) {
        log.debug("Inside @method getEventDetailsBySubjectAndTime. @param: email -> {}, subject -> {}, startDateTime -> {}, endDateTime -> {}",
                email, subject, startDateTime, endDateTime);

        try {
            if (!EmailUtils.checkDomain(email, orgDomainName)) {
                log.warn("Email domain not allowed: {}", email);
                return new ArrayList<>();
            }

            if (!allowOutsiders) {
                boolean userExists = emailUserDao.existsByEmailAndNotDeleted(email);
                if (!userExists) {
                    log.warn("User not found or deleted: {}", email);
                    return new ArrayList<>();
                }
            }

            Date startDate = convertStringToDate(startDateTime);
            Date endDate = convertStringToDate(endDateTime);

            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            CalendarFolder cf = CalendarFolder.bind(service, WellKnownFolderName.Calendar);
            CalendarView view = new CalendarView(startDate, endDate);
            view.setMaxItemsReturned(100);

            FindItemsResults<Appointment> response = cf.findAppointments(view);
            List<EventDto> events = new ArrayList<>();

            for (Appointment appointment : response.getItems()) {
                appointment.load(PropertySet.FirstClassProperties);

                // Filter by subject if provided
                if (subject != null && !subject.isEmpty() &&
                        appointment.getSubject() != null &&
                        appointment.getSubject().toLowerCase().contains(subject.toLowerCase())) {

                    if (!appointment.getIsCancelled()) {
                        EventDto eventDto = ConverterUtils.convertToEventDto(appointment);
                        events.add(eventDto);
                    }
                }
            }

            log.debug("Found {} events matching subject '{}' for email: {}", events.size(), subject, email);
            return events;

        } catch (Exception e) {
            log.error("Error inside @method getEventDetailsBySubjectAndTime: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<EventDto> getEventDetails(String email, String eventId, String startDateTime, String endDateTime) {
        log.debug("Inside @method getEventDetails. @param: email -> {}, eventId -> {}, startDateTime -> {}, endDateTime -> {}",
                email, eventId, startDateTime, endDateTime);

        try {
            if (!EmailUtils.checkDomain(email, orgDomainName)) {
                log.warn("Email domain not allowed: {}", email);
                return new ArrayList<>();
            }

            if (!allowOutsiders) {
                boolean userExists = emailUserDao.existsByEmailAndNotDeleted(email);
                if (!userExists) {
                    log.warn("User not found or deleted: {}", email);
                    return new ArrayList<>();
                }
            }

            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the specific appointment by its ID
            ItemId itemId = new ItemId(eventId);
            Appointment appointment = Appointment.bind(service, itemId);
            appointment.load(PropertySet.FirstClassProperties);

            List<EventDto> events = new ArrayList<>();

            if (!appointment.getIsCancelled()) {
                // Check if the appointment falls within the specified date range
                Date startDate = convertStringToDate(startDateTime);
                Date endDate = convertStringToDate(endDateTime);

                if ((appointment.getStart().equals(startDate) || appointment.getStart().after(startDate)) &&
                        (appointment.getEnd().equals(endDate) || appointment.getEnd().before(endDate))) {

                    EventDto eventDto = ConverterUtils.convertToEventDto(appointment);
                    events.add(eventDto);
                }
            }

            log.debug("Found {} event details for eventId: {}", events.size(), eventId);
            return events;

        } catch (Exception e) {
            log.error("Error inside @method getEventDetails: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public AvailableSlots getAvailableSlotsAndConflictV1(List<String> emails, String startDateTime, String endDateTime, int slotDuration, Boolean workingFlag) throws Exception {
        return null;
    }

    public List<EventDto> getCalendarEvents(String email, String startDateTime, String endDateTime, boolean forAvailability) {
        log.debug("Inside @method getCalendarEvents. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", email, startDateTime, endDateTime);
        if (!EmailUtils.checkDomain(email, orgDomainName)) return new ArrayList<>();

        if (!allowOutsiders) {
            boolean userExists = emailUserDao.existsByEmailAndNotDeleted(email);
            if (!userExists) return new ArrayList<>();
        }

        List<EventDto> eventDtos = new ArrayList<>();
        EmailUser user = emailUserDao.findByEmail(email);
        if (user == null) {
            log.warn("No user found for email: {}", email);
            return eventDtos; // Return an empty list
        }
        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
        String timeZone = preferences.getTimeZone();

        Date startDate;
        Date endDate;
        if (startDateTime == null || endDateTime == null) {
            startDate = DateUtils.getFormattedDateTime(false, timeZone);
            endDate = DateUtils.getFormattedDateTime(true, timeZone);
        } else {
            if (startDateTime.endsWith("Z")) {
                startDate = DateUtils.parseDate(startDateTime);
                endDate = DateUtils.parseDate(endDateTime);
            } else {
                startDate = DateUtils.parseDateWithTimeZone(startDateTime, timeZone);
                endDate = DateUtils.parseDateWithTimeZone(endDateTime, timeZone);
            }
        }

        List<EventDto> events = new ArrayList<>();

        try {
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);
            CalendarFolder cf = CalendarFolder.bind(service, WellKnownFolderName.Calendar);
            CalendarView view = new CalendarView(startDate, endDate);
            view.setMaxItemsReturned(100);
            FindItemsResults<Appointment> response = cf.findAppointments(view);
            for (Appointment appointment : response.getItems()) {
                appointment.load(PropertySet.FirstClassProperties);
                if (!appointment.getIsCancelled() &&
                        !appointment.getRequiredAttendees().getItems().stream()
                                .allMatch(res -> res.getResponseType().toString().equals("Decline"))) {

                    EventDto eventDto = ConverterUtils.convertToEventDto(appointment);
                    events.add(eventDto);
                }
            }
            return events;
        } catch (Exception e) {
            log.error("Error inside @method getCalendarEvents", e);
        }
        return new ArrayList<>();
    }


    @Override
    public String getSchedule(List<String> emails, String startDateTime, String endDateTime, Integer slotDuration, Boolean workingFlag) {
        log.debug("Inside @method getSchedule. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}, slotDuration -> {}",
                emails, startDateTime, endDateTime, slotDuration);

        try {
            // Parse dates
            Date startDate = convertStringToDate(startDateTime);
            Date endDate = convertStringToDate(endDateTime);

            // Create the response structure matching Graph API format
            Map<String, Object> response = new HashMap<>();
            response.put("@odata.context", "https://graph.microsoft.com/v1.0/$metadata#Collection(microsoft.graph.scheduleInformation)");

            List<Map<String, Object>> scheduleInfoList = new ArrayList<>();

            for (String email : emails) {
                try {
                    Map<String, Object> scheduleInfo = getScheduleInfoForUser(email, startDate, endDate, slotDuration);
                    if (scheduleInfo != null) {
                        scheduleInfoList.add(scheduleInfo);
                    }
                } catch (Exception e) {
                    log.error("Error getting schedule for user {}: {}", email, e.getMessage(), e);
                    // Continue with other users even if one fails
                }
            }

            response.put("value", scheduleInfoList);
            return JsonUtils.convertToJSON(response);

        } catch (Exception e) {
            log.error("Error in getSchedule: {}", e.getMessage(), e);
            return "{}";
        }
    }

    private Map<String, Object> getScheduleInfoForUser(String email, Date startDate, Date endDate, Integer slotDuration) throws Exception {
        ExchangeService service = EWSUtils.getServiceObjectForUser(email);

        // Store original end date for response filtering
        Date originalEndDate = endDate;

        // Check if time window is less than 24 hours and extend if needed
        long timeDifferenceHours = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60);
        Date ewsEndDate = endDate;

        if (timeDifferenceHours < 24) {
            // Extend end date to 24 hours from start date for EWS request
            ewsEndDate = new Date(startDate.getTime() + (24 * 60 * 60 * 1000L));
            log.debug("Extended end date from {} to {} for EWS 24-hour minimum requirement",
                    originalEndDate, ewsEndDate);
        }

        // Create attendee info for the user
        List<AttendeeInfo> attendees = new ArrayList<>();
        AttendeeInfo attendee = new AttendeeInfo();
        attendee.setSmtpAddress(email);
        attendee.setAttendeeType(MeetingAttendeeType.Required);
        attendees.add(attendee);

        // Set up availability options
        AvailabilityOptions availabilityOptions = new AvailabilityOptions();
        availabilityOptions.setRequestedFreeBusyView(FreeBusyViewType.FreeBusy);

        // Create time window with potentially extended end date
        TimeWindow timeWindow = new TimeWindow(startDate, ewsEndDate);

        // Get user availability
        GetUserAvailabilityResults results = service.getUserAvailability(
                attendees,
                timeWindow,
                AvailabilityData.FreeBusy,
                availabilityOptions
        );

        if (results.getAttendeesAvailability().getCount() == 0) {
            return new HashMap<>();
        }

        AttendeeAvailability availability = results.getAttendeesAvailability().getResponseAtIndex(0);

        // Build the schedule info response
        Map<String, Object> scheduleInfo = new HashMap<>();
        scheduleInfo.put("scheduleId", email);

        // Generate availability view (filtered to original time range)
        String availabilityView = generateAvailabilityView(availability, startDate, originalEndDate, slotDuration);
        scheduleInfo.put("availabilityView", availabilityView);

        // Add schedule items from calendar events (filtered to original time range)
        List<Map<String, Object>> scheduleItems = convertCalendarEventsToScheduleItems(availability.getCalendarEvents(), originalEndDate);
        scheduleInfo.put("scheduleItems", scheduleItems);

        // Add working hours
        WorkingHours workingHours = availability.getWorkingHours();
        if (workingHours != null) {
            scheduleInfo.put("workingHours", convertWorkingHours(workingHours));
        } else {
            // Default working hours if not available
            scheduleInfo.put("workingHours", getDefaultWorkingHours());
        }

        return scheduleInfo;
    }

    private String generateAvailabilityView(AttendeeAvailability availability, Date startDate, Date endDate, Integer slotDuration) {
        StringBuilder availabilityView = new StringBuilder();

        try {
            long intervalMillis = slotDuration * 60 * 1000;

            Date currentTime = new Date(startDate.getTime());

            while (currentTime.before(endDate)) {
                Date slotEnd = new Date(currentTime.getTime() + intervalMillis);

                // Check if this time slot has any busy events
                boolean isBusy = false;
                for (CalendarEvent event : availability.getCalendarEvents()) {
                    if (event.getStartTime().before(slotEnd) && event.getEndTime().after(currentTime)) {
                        // There's an overlap, check busy type
                        switch (event.getFreeBusyStatus().toString()) {
                            case "Busy":
                                availabilityView.append("2");
                                isBusy = true;
                                break;
                            case "Tentative":
                                availabilityView.append("1");
                                isBusy = true;
                                break;
                            case "OutOfOffice":
                                availabilityView.append("3");
                                isBusy = true;
                                break;
                            case "WorkingElsewhere":
                                availabilityView.append("4");
                                isBusy = true;
                                break;
                            default:
                                break;
                        }
                        if (isBusy) break;
                    }
                }

                if (!isBusy) {
                    availabilityView.append("0"); // Free
                }

                currentTime = slotEnd;
            }
        } catch (Exception e) {
            log.error("Error generating availability view: {}", e.getMessage(), e);
            return "0"; // Default to free if error
        }

        return availabilityView.toString();
    }

    private List<Map<String, Object>> convertCalendarEventsToScheduleItems(Iterable<CalendarEvent> calendarEvents, Date originalEndDate) {
        List<Map<String, Object>> scheduleItems = new ArrayList<>();

        try {
            SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS");

            for (CalendarEvent event : calendarEvents) {
                // Only include busy events (not free time) and within original time range
                if (!"Free".equals(event.getFreeBusyStatus().toString()) &&
                        event.getStartTime().before(originalEndDate)) {

                    Map<String, Object> scheduleItem = new HashMap<>();

                    // Add start and end times (but cap end time to original end date if needed)
                    Date eventEndTime = event.getEndTime().after(originalEndDate) ? originalEndDate : event.getEndTime();

                    Map<String, Object> start = new HashMap<>();
                    start.put("dateTime", isoFormat.format(event.getStartTime()));
                    start.put("timeZone", "UTC");
                    scheduleItem.put("start", start);

                    Map<String, Object> end = new HashMap<>();
                    end.put("dateTime", isoFormat.format(eventEndTime));
                    end.put("timeZone", "UTC");
                    scheduleItem.put("end", end);

                    // Add status based on busy type
                    String status = mapBusyTypeToStatus(event.getFreeBusyStatus().toString());
                    scheduleItem.put("status", status);

                    // Add subject if available (may be null for free/busy only requests)
                    scheduleItem.put("subject", event.getDetails() != null ? event.getDetails().getSubject() : "");

                    scheduleItems.add(scheduleItem);
                }
            }
        } catch (Exception e) {
            log.error("Error converting calendar events to schedule items: {}", e.getMessage(), e);
        }

        return scheduleItems;
    }

    private String mapBusyTypeToStatus(String busyType) {
        return switch (busyType) {
            case "Busy" -> "busy";
            case "Tentative" -> "tentative";
            case "OutOfOffice" -> "oof";
            case "WorkingElsewhere" -> "workingElsewhere";
            default -> "free";
        };
    }

    private Map<String, Object> convertWorkingHours(WorkingHours workingHours) {
        Map<String, Object> workingHoursMap = new HashMap<>();

        try {
            // Convert days of week
            List<String> daysOfWeek = new ArrayList<>();
            if (workingHours.getDaysOfTheWeek() != null) {
                String daysString = workingHours.getDaysOfTheWeek().toString();
                if (daysString.contains("Monday")) daysOfWeek.add("monday");
                if (daysString.contains("Tuesday")) daysOfWeek.add("tuesday");
                if (daysString.contains("Wednesday")) daysOfWeek.add("wednesday");
                if (daysString.contains("Thursday")) daysOfWeek.add("thursday");
                if (daysString.contains("Friday")) daysOfWeek.add("friday");
                if (daysString.contains("Saturday")) daysOfWeek.add("saturday");
                if (daysString.contains("Sunday")) daysOfWeek.add("sunday");
            }

            if (daysOfWeek.isEmpty()) {
                // Default to weekdays
                daysOfWeek.addAll(List.of("monday", "tuesday", "wednesday", "thursday", "sunday"));
            }

            workingHoursMap.put("daysOfWeek", daysOfWeek);

            long startMinutesLong = workingHours.getStartTime();
            int startHours = (int) startMinutesLong / 60;
            int startMinutes = (int) startMinutesLong % 60;
            String startTime = String.format("%02d:%02d:00.0000000", startHours, startMinutes);


            long endMinutesLong = workingHours.getEndTime(); // returns long, total startMinutes since midnight
            int endHours = (int) (endMinutesLong / 60);
            int endMinutes = (int) (endMinutesLong % 60);
            String endTime = String.format("%02d:%02d:00.0000000", endHours, endMinutes);


            workingHoursMap.put("startTime", startTime);
            workingHoursMap.put("endTime", endTime);

            // Add timezone information
            Map<String, String> timeZone = new HashMap<>();
            timeZone.put("name", workingHours.getTimeZone() != null ? workingHours.getTimeZone().getName() : null); // Default timezone
            workingHoursMap.put("timeZone", timeZone);

        } catch (Exception e) {
            log.error("Error converting working hours: {}", e.getMessage(), e);
            return getDefaultWorkingHours();
        }

        return workingHoursMap;
    }

    private Map<String, Object> getDefaultWorkingHours() {
        log.warn("Working hours not available, using default working hours");

        Map<String, Object> defaultWorkingHours = new HashMap<>();
        defaultWorkingHours.put("daysOfWeek", List.of("sunday", "monday", "tuesday", "wednesday", "thursday"));
        defaultWorkingHours.put("startTime", "08:00:00.0000000");
        defaultWorkingHours.put("endTime", "17:00:00.0000000");

        Map<String, String> timeZone = new HashMap<>();
        timeZone.put("name", "Asia/Riyadh");
        defaultWorkingHours.put("timeZone", timeZone);

        return defaultWorkingHours;
    }

    @Override
    public AvailabilityResponse getAvailability(List<String> emails, String startDateTime, String endDateTime, int slotDuration, Boolean workingFlag) throws Exception {
        log.debug("Inside @method getAvailability. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}, slotDuration -> {}",
                emails, startDateTime, endDateTime, slotDuration);

        try {
            // Get schedule information using our getSchedule method
            String scheduleJson = getSchedule(emails, startDateTime, endDateTime, slotDuration, workingFlag);

            // Parse the JSON response
            Map<String, Object> scheduleResponse = JsonUtils.convertJsonToObject(scheduleJson, Map.class);
            List<Map<String, Object>> scheduleInfoArray = (List<Map<String, Object>>) scheduleResponse.get("value");

            if (scheduleInfoArray == null || scheduleInfoArray.isEmpty()) {
                return new AvailabilityResponse(new ArrayList<>(), new ArrayList<>());
            }

            // Parse dates
            Date start = convertStringToDate(startDateTime);
            Date end = convertStringToDate(endDateTime);

            // Create time slots from availability data
            List<TimeSlot> slots = createTimeSlotsFromScheduleInfo(scheduleInfoArray, start, end, slotDuration, emails);

            // Get available meeting slots
            List<Meeting> availableSlots = getAvailableMeetingSlotsFromSchedule(scheduleInfoArray, start, end, slotDuration);

            return new AvailabilityResponse(slots, availableSlots);

        } catch (Exception e) {
            log.error("Error in getAvailability: {}", e.getMessage(), e);
            return new AvailabilityResponse(new ArrayList<>(), new ArrayList<>());
        }
    }

    private List<TimeSlot> createTimeSlotsFromScheduleInfo(List<Map<String, Object>> scheduleInfoArray,
                                                           Date start, Date end, int slotDuration, List<String> emails) {
        List<TimeSlot> slots = new ArrayList<>();

        try {
            // Create availability map from schedule info
            Map<String, String> availabilityMap = new HashMap<>();
            for (Map<String, Object> scheduleInfo : scheduleInfoArray) {
                String scheduleId = (String) scheduleInfo.get("scheduleId");
                String availabilityView = (String) scheduleInfo.get("availabilityView");
                if (availabilityView != null) {
                    availabilityMap.put(scheduleId, availabilityView);
                }
            }

            // Calculate time slots
            long slotDurationMillis = slotDuration * 60 * 1000L;
            Date currentTime = new Date(start.getTime());

            while (currentTime.before(end)) {
                Date slotEnd = new Date(currentTime.getTime() + slotDurationMillis);
                if (slotEnd.after(end)) {
                    slotEnd = end;
                }

                List<AvailabilityStatus> availability = new ArrayList<>();

                // Calculate slot index for availability view
                long slotIndex = (currentTime.getTime() - start.getTime()) / (30 * 60 * 1000L); // 30-minute intervals

                for (String email : emails) {
                    String availabilityView = availabilityMap.get(email);
                    String status = "free"; // default

                    if (availabilityView != null && slotIndex < availabilityView.length()) {
                        char statusChar = availabilityView.charAt((int) slotIndex);
                        status = mapAvailabilityCharToStatus(statusChar);
                    }

                    availability.add(new AvailabilityStatus(email, status, null));
                }

                slots.add(new TimeSlot(currentTime, slotEnd, availability));
                currentTime = slotEnd;
            }

        } catch (Exception e) {
            log.error("Error creating time slots from schedule info: {}", e.getMessage(), e);
        }

        return slots;
    }

    private String mapAvailabilityCharToStatus(char statusChar) {
        return switch (statusChar) {
            case '0' -> "free";
            case '1' -> "tentative";
            case '2' -> "busy";
            case '3' -> "oof"; // out of office
            case '4' -> "workingElsewhere";
            default -> "unknown";
        };
    }

    private List<Meeting> getAvailableMeetingSlotsFromSchedule(List<Map<String, Object>> scheduleInfoArray,
                                                               Date start, Date end, int slotDuration) {
        List<Meeting> availableSlots = new ArrayList<>();

        try {
            // Create a map of all busy times for all users
            Map<String, List<Meeting>> allBusyTimes = new HashMap<>();

            for (Map<String, Object> scheduleInfo : scheduleInfoArray) {
                String scheduleId = (String) scheduleInfo.get("scheduleId");
                String availabilityView = (String) scheduleInfo.get("availabilityView");

                List<Meeting> busyTimes = new ArrayList<>();

                if (availabilityView != null) {
                    // Parse availability view to extract busy times
                    long slotDurationMillis = 30 * 60 * 1000L; // 30-minute intervals
                    Date currentTime = new Date(start.getTime());

                    for (int i = 0; i < availabilityView.length() && currentTime.before(end); i++) {
                        char statusChar = availabilityView.charAt(i);
                        Date slotEnd = new Date(currentTime.getTime() + slotDurationMillis);

                        if (statusChar == '2' || statusChar == '3') { // busy or out of office
                            Meeting busySlot = new Meeting();
                            busySlot.setStartTime(currentTime);
                            busySlot.setEndTime(slotEnd);
                            busyTimes.add(busySlot);
                        }

                        currentTime = slotEnd;
                    }
                }

                allBusyTimes.put(scheduleId, busyTimes);
            }

            // Use the existing method to find available time slots
            long meetingDurationMillis = slotDuration * 60 * 1000L;
            availableSlots = EventUtils.findAvailableTimeSlots(allBusyTimes, start, end, meetingDurationMillis);

        } catch (Exception e) {
            log.error("Error getting available meeting slots from schedule: {}", e.getMessage(), e);
        }

        return availableSlots;
    }


    @Override
    public Map<String, String> cancelEvent(String eventId, String comment) throws Exception {
        log.debug("Inside @method cancelEvent. @param: eventId -> {}, comment -> {}", eventId, comment);

        try {
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the appointment by its ID
            ItemId itemId = new ItemId(eventId);
            Appointment appointment = Appointment.bind(service, itemId);
            appointment.load(PropertySet.FirstClassProperties);

            // Cancel the appointment with the provided comment
            if (comment != null && !comment.isEmpty()) {
                appointment.setBody(MessageBody.getMessageBodyFromText(comment));
            }

            // Cancel and send cancellation to all attendees
            appointment.cancelMeeting(comment);

            log.debug("Event cancelled successfully: {}", eventId);
            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);

        } catch (Exception e) {
            log.error("Error inside @method cancelEvent: {}", e.getMessage(), e);
            throw new Exception("Failed to cancel event: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, String> forwardEvent(String eventId, List<String> emailIds, String comment) {
        log.debug("Inside @method forwardEvent. @param: eventId -> {}, emailIds -> {}, comment -> {}", eventId, emailIds, comment);

        try {
            String email = userContextHolder.getCurrentUser().getEmail();
            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the appointment by its ID
            ItemId itemId = new ItemId(eventId);
            Appointment appointment = Appointment.bind(service, itemId);
            appointment.load(PropertySet.FirstClassProperties);

            // Create a new appointment based on the original one
            Appointment forwardedAppointment = new Appointment(service);
            forwardedAppointment.setSubject("Fwd: " + appointment.getSubject());

            // Set body with forward comment
            String forwardBody = "";
            if (comment != null && !comment.isEmpty()) {
                forwardBody = comment + "\n\n--- Forwarded Event ---\n";
            } else {
                forwardBody = "--- Forwarded Event ---\n";
            }

            if (appointment.getBody() != null) {
                forwardBody += appointment.getBody().toString();
            }

            forwardedAppointment.setBody(MessageBody.getMessageBodyFromText(forwardBody));
            forwardedAppointment.setStart(appointment.getStart());
            forwardedAppointment.setEnd(appointment.getEnd());
            forwardedAppointment.setLocation(appointment.getLocation());

            // Add recipients as required attendees
            for (String emailId : emailIds) {
                forwardedAppointment.getRequiredAttendees().add(emailId);
            }

            // Save and send the forwarded appointment
            forwardedAppointment.save(WellKnownFolderName.Calendar, SendInvitationsMode.SendToAllAndSaveCopy);

            log.debug("Event forwarded successfully to: {}", emailIds);
            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);

        } catch (Exception e) {
            log.error("Error inside @method forwardEvent: {}", e.getMessage(), e);
            return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
    }

    @Override
    public EventDto getCalendarEventByEventId(String eventId) throws Exception {
        log.debug("Inside @method getCalendarEventByEventId. @param: eventId -> {}", eventId);

        try {
            String email = userContextHolder.getCurrentUser().getEmail();
            if (email == null || email.isEmpty()) {
                throw new RuntimeException("User email not found in context");
            }

            ExchangeService service = EWSUtils.getServiceObjectForUser(email);

            // Bind to the appointment by its ID
            ItemId itemId = new ItemId(eventId);
            Appointment appointment = Appointment.bind(service, itemId);
            appointment.load(PropertySet.FirstClassProperties);

            if (appointment.getIsCancelled()) {
                log.warn("Requested event is cancelled: {}", eventId);
                return null;
            }

            EventDto eventDto = ConverterUtils.convertToEventDto(appointment);
            log.debug("Successfully retrieved event: {}", eventId);
            return eventDto;

        } catch (Exception e) {
            log.error("Error inside @method getCalendarEventByEventId: {}", e.getMessage(), e);
            throw new Exception("Failed to get calendar event by ID: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Meeting> getAvailableMeetingSlots(List<String> emails, String startDateTime, String endDateTime, int slotDuration) {
        try {
            log.debug("Inside @method getAvailableMeetingSlots. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}", emails, startDateTime, endDateTime);
            if (startDateTime == null || endDateTime == null) {
                String email = userContextHolder.getCurrentUser().getEmail();
//                EmailUser user = emailUserDao.findByEmail(email);
//                String userId = user.getUserId();
                EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
                startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
                endDateTime = DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
            }
            Map<String, List<Meeting>> result = new HashMap<>();
            for (String email : emails) {
                List<EventDto> calendarEvents = getCalendarEvents(email, startDateTime, endDateTime, true);
                if (calendarEvents == null || calendarEvents.isEmpty()) {
                    log.debug("No calendar events found for email: {}", email);
                    result.put(email, new ArrayList<>());  // Add empty list for this email
                    continue;
                }
                List<Meeting> list = new ArrayList<>();
                for (EventDto eventDto : calendarEvents) {
                    Meeting meeting = new Meeting();
                    meeting.setStartTime(eventDto.getMeetingStartTime());
                    meeting.setEndTime(eventDto.getMeetingEndTime());
                    list.add(meeting);
                }
                result.put(email, list);
            }
            Date start, end;
            if (startDateTime.endsWith("Z")) {
                start = convertStringToDate(startDateTime);
                end = convertStringToDate(endDateTime);
            } else {
                start = DateUtils.parseDateWithoutTZ(startDateTime);
                end = DateUtils.parseDateWithoutTZ(endDateTime);
            }

            long durationMillis = (long) slotDuration * 60 * 1000;
            return EventUtils.findAvailableTimeSlots(result, start, end, durationMillis);
        } catch (Exception e) {
            log.error("Error inside @method getAvailableSlot : {} ", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public Map<String, Object> updateUserDetails(GraphUserDto graphUserDto) {
        log.debug("Inside @method updateUserDetails. @param: graphUserDto -> {}", graphUserDto);

        // EWS doesn't support user management operations like Graph API
        // This would typically require Active Directory operations or other mechanisms
        // For EWS implementation, we'll log the attempt and return a not supported response

        log.warn("updateUserDetails is not supported in EWS implementation. User management should be done through Active Directory or other user management systems.");

        Map<String, Object> result = new HashMap<>();
        result.put(EmailConstants.RESULT, EmailConstants.FAILED);
        result.put("message", "User details update not supported in EWS implementation");
        result.put("reason", "EWS does not provide user management capabilities. Use Active Directory or Graph API instead.");

        return result;
    }

    @Override
    public Map<String, Object> createUser(Map<String, Object> userRequest) {
        log.debug("Inside @method createUser. @param: userRequest -> {}", userRequest);

        // EWS doesn't support user creation operations like Graph API
        // This would typically require Active Directory operations or other mechanisms
        // For EWS implementation, we'll log the attempt and return a not supported response

        log.warn("createUser is not supported in EWS implementation. User creation should be done through Active Directory or other user management systems.");

        Map<String, Object> result = new HashMap<>();
        result.put(EmailConstants.RESULT, EmailConstants.FAILED);
        result.put("message", "User creation not supported in EWS implementation");
        result.put("reason", "EWS does not provide user management capabilities. Use Active Directory or Graph API instead.");

        return result;
    }

    @Override
    public Map<String, Object> assignLicenseToUser(String userPrincipalName) {
        log.debug("Inside @method assignLicenseToUser. @param: userPrincipalName -> {}", userPrincipalName);

        // EWS doesn't support license assignment operations like Graph API
        // This would typically require Microsoft 365 admin operations or other mechanisms
        // For EWS implementation, we'll log the attempt and return a not supported response

        log.warn("assignLicenseToUser is not supported in EWS implementation. License assignment should be done through Microsoft 365 Admin Center or Graph API.");

        Map<String, Object> result = new HashMap<>();
        result.put(EmailConstants.RESULT, EmailConstants.FAILED);
        result.put("message", "License assignment not supported in EWS implementation");
        result.put("reason", "EWS does not provide license management capabilities. Use Microsoft 365 Admin Center or Graph API instead.");

        return result;
    }

    @Override
    public Map<String, Object> setAutomaticRepliesSettings(String userId, SetAutomaticRepliesRequest request) throws Exception {
        log.debug("Inside @method setAutomaticRepliesSettings. @param: userId -> {}, request -> {}", userId, request);

        try {
            // Create the ExchangeService object
            ExchangeService service = EWSUtils.getServiceObjectForUser(userId);

            // Create and configure OOF settings
            OofSettings oofSettings = createOofSettingsFromRequest(request);

            // Update the OutOfOffice settings for the user
            service.setUserOofSettings(userId, oofSettings);

            log.info("Auto-reply has been successfully set for user: {}", userId);

            // Return success response
            return createSuccessResponse("Automatic replies settings updated successfully");

        } catch (Exception e) {
            log.error("Error setting automatic replies settings for user {}: {}", userId, e.getMessage(), e);

            // Return error response
            return createErrorResponse(e.getMessage());
        }
    }

    /**
     * Creates OOF settings from the request object
     *
     * @param request The automatic replies request
     * @return Configured OofSettings object
     * @throws Exception if date parsing fails
     */
    private OofSettings createOofSettingsFromRequest(SetAutomaticRepliesRequest request) throws Exception {
        // Extract values from the request
        String internalReplyMessage = request.getInternalReplyMessage();
        String externalReplyMessage = request.getExternalReplyMessage();
        String scheduledStartDateTime = request.getScheduledStartDateTime().getDateTime();
        String scheduledEndDateTime = request.getScheduledEndDateTime().getDateTime();
        String timeZone = request.getScheduledStartDateTime().getTimeZone();

        // Parse dates and create time window
        TimeWindow timeWindow = parseDateTimeAndCreateTimeWindow(scheduledStartDateTime, scheduledEndDateTime, timeZone);

        // Set up OutOfOfficeSettings
        OofSettings oofSettings = new OofSettings();

        // Set the state based on the request status
        configureOofState(oofSettings, request.getStatus(), timeWindow);

        // Set internal and external reply messages
        configureReplyMessages(oofSettings, internalReplyMessage, externalReplyMessage);

        return oofSettings;
    }

    /**
     * Parses date strings and creates a TimeWindow object
     *
     * @param startDateTime Start date time string
     * @param endDateTime   End date time string
     * @param timeZone      Timezone string
     * @return TimeWindow object
     * @throws Exception if date parsing fails
     */
    private TimeWindow parseDateTimeAndCreateTimeWindow(String startDateTime, String endDateTime, String timeZone) throws Exception {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        dateFormat.setTimeZone(TimeZone.getTimeZone(timeZone));

        Date startDate = dateFormat.parse(startDateTime);
        Date endDate = dateFormat.parse(endDateTime);

        return new TimeWindow(startDate, endDate);
    }

    /**
     * Configures the OOF state based on the status
     *
     * @param oofSettings The OOF settings object to configure
     * @param status      The status string from the request
     * @param timeWindow  The time window for scheduled OOF
     */
    private void configureOofState(OofSettings oofSettings, String status, TimeWindow timeWindow) {
        if ("alwaysEnabled".equals(status)) {
            oofSettings.setState(OofState.Enabled);
        } else if ("scheduled".equals(status)) {
            oofSettings.setState(OofState.Scheduled);
            oofSettings.setDuration(timeWindow);
        } else {
            oofSettings.setState(OofState.Disabled);
        }
    }

    /**
     * Configures reply messages for internal and external recipients
     *
     * @param oofSettings          The OOF settings object to configure
     * @param internalReplyMessage Internal reply message
     * @param externalReplyMessage External reply message
     */
    private void configureReplyMessages(OofSettings oofSettings, String internalReplyMessage, String externalReplyMessage) {
        if (internalReplyMessage != null && !internalReplyMessage.trim().isEmpty()) {
            oofSettings.setInternalReply(new OofReply(internalReplyMessage));
        }

        if (externalReplyMessage != null && !externalReplyMessage.trim().isEmpty()) {
            oofSettings.setExternalReply(new OofReply(externalReplyMessage));
        }
    }

    /**
     * Creates a success response map
     *
     * @param message Success message
     * @return Map with success result
     */
    private Map<String, Object> createSuccessResponse(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put(EmailConstants.RESULT, EmailConstants.SUCCESS);
        result.put("message", message);
        return result;
    }

    /**
     * Creates an error response map
     *
     * @param errorMessage Error message
     * @return Map with error result
     */
    private Map<String, Object> createErrorResponse(String errorMessage) {
        Map<String, Object> result = new HashMap<>();
        result.put(EmailConstants.RESULT, EmailConstants.FAILED);
        result.put("error", errorMessage);
        return result;
    }

    /**
     * Finds an appointment by eventId by searching through the calendar
     * This method handles the case where eventId might be a Graph API ID
     */
    private Appointment findAppointmentByEventId(ExchangeService service, String eventId, String email) {
        try {
            ItemId itemId = new ItemId(eventId);
            return Appointment.bind(service, itemId);
        } catch (Exception e) {
            log.debug("EventId is not a valid EWS ItemId, searching by subject or other criteria: {}", e.getMessage());
            throw new BusinessException("EventId is not a valid EWS ItemId");
        }
    }


}
