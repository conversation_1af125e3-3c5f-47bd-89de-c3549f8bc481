package com.enttribe.emailagent.service;

import com.enttribe.emailagent.dto.AvailabilityResponse;
import com.enttribe.emailagent.dto.AvailableSlots;
import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.dto.GraphUserDto;
import com.enttribe.emailagent.dto.Meeting;
import com.enttribe.emailagent.dto.SetAutomaticRepliesRequest;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

public interface EventService {

    List<EventDto> getCalendarEventsV1(String email, String startDateTime, String endDateTime, String subject) ;

    Map<String, Object> updateEvent(String eventId, String email, String meetingRequestJson);

    Map<String, String> declineMeeting(String eventId);

    Map<String, String> acceptMeeting(String eventId);

    Map<String, String> tentativelyAccept(String eventId);

    Map<String, Object> scheduleEvent(String email, Map<String, Object> requestBody);

    Map<String, String> rescheduleEvent(Map<String, String> requestBody);

    List<EventDto> getEventDetailsBySubjectAndTime(String email, String subject, String startDateTime, String endDateTime);

    List<EventDto> getEventDetails(String email, String eventId, String startDateTime, String endDateTime);

    AvailableSlots getAvailableSlotsAndConflictV1(
            List<String> emails,
            String startDateTime,
            String endDateTime,
            int slotDuration,
            Boolean workingFlag)
            throws Exception;

    String getSchedule(
            List<String> emails,
            String startDateTime,
            String endDateTime,
            Integer slotDuration,
            Boolean workingFlag);

    AvailabilityResponse getAvailability(
            List<String> emails,
            String startDateTime,
            String endDateTime,
            int slotDuration,
            Boolean workingFlag)
            throws Exception;

    Map<String, String> cancelEvent(String eventId, String comment) throws Exception;

    Map<String, String> forwardEvent(String eventId, List<String> emailIds, String comment)
            throws IOException, InterruptedException;

    EventDto getCalendarEventByEventId(String eventId) throws Exception;

    List<Meeting> getAvailableMeetingSlots(List<String> emails, String startDateTime, String endDateTime, int slotDuration);

    Map<String, Object> updateUserDetails(GraphUserDto graphUserDto);

    Map<String, Object> createUser(Map<String, Object> userRequest);

    Map<String, Object> assignLicenseToUser(String userPrincipalName);

    Map<String, Object> setAutomaticRepliesSettings(String userId, SetAutomaticRepliesRequest request) throws Exception;

    Map<String, Object> updateEventV1(String eventId, String email, Map<String, Object> jsonBody);

}
