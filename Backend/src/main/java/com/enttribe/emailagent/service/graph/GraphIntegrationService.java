package com.enttribe.emailagent.service.graph;

import static com.enttribe.emailagent.utils.WorkingHoursUtil.GRAPH_API_TO_IANA_TIMEZONE;

import com.enttribe.emailagent.constant.EmailConstants;
import com.enttribe.emailagent.dao.EmailPreferencesDao;
import com.enttribe.emailagent.dao.EmailUserDao;
import com.enttribe.emailagent.dto.AttendeeAndStatus;
import com.enttribe.emailagent.dto.AvailabilityResponse;
import com.enttribe.emailagent.dto.AvailabilityStatus;
import com.enttribe.emailagent.dto.AvailableSlots;
import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.dto.GraphUserDto;
import com.enttribe.emailagent.dto.Meeting;
import com.enttribe.emailagent.dto.TimeSlot;
import com.enttribe.emailagent.dto.WorkingHoursSummary;
import com.enttribe.emailagent.dto.SetAutomaticRepliesRequest;
import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.exception.ApiException;
import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.service.EventService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.CommonUtils;
import com.enttribe.emailagent.utils.DateUtils;
import com.enttribe.emailagent.utils.EWSUtils;
import com.enttribe.emailagent.utils.EmailUtils;
import com.enttribe.emailagent.utils.TokenUtils;
import com.enttribe.emailagent.utils.WorkingHoursUtil;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.enumeration.property.OofExternalAudience;
import microsoft.exchange.webservices.data.core.enumeration.property.OofState;
import microsoft.exchange.webservices.data.property.complex.availability.OofSettings;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "email.platform", havingValue = "graph", matchIfMissing = true)
public class GraphIntegrationService implements EventService {

  // Static constants
  private static final String GRAPH_API_CANCEL_EVENT = "https://graph.microsoft.com/v1.0/users/%s/events/%s/cancel";
  private static final String GRAPH_API_FORWARD_EVENT = "https://graph.microsoft.com/v1.0/users/%s/events/%s/forward";
  private static final String GRAPH_API_GET_EVENT = "https://graph.microsoft.com/v1.0/users/%s/events/%s";
  private static final String CALENDAR_GET_SCHEDULE_PATH = "/calendar/getSchedule";
  private static final String GRAPH_API_USERS_BASE_URL = "https://graph.microsoft.com/v1.0/users/";

  // Magic numbers and duplicated literals as constants
  private static final int HTTP_STATUS_OK = 200;
  private static final int HTTP_STATUS_CREATED = 201;
  private static final int MINUTES_PER_HOUR = 60;
  private static final long MILLIS_PER_SECOND = 1000L;
  private static final int DEFAULT_SLOT_DURATION = 60; // Default slot duration in minutes
  private static final int DEFAULT_AVAILABILITY_SLOT_DURATION = 30; // Default availability slot duration in minutes
  private static final int MAX_MEETING_RESULTS = 4; // For firstN(meetingList, 4)
  private static final int DAYS_AHEAD = 7; // For plusDays(7)
  private static final int HTTP_STATUS_NO_CONTENT = 204;
  private static final int HTTP_STATUS_ACCEPTED = 202;
  private static final int TIME_STRING_LENGTH = 8;
  // 'Bearer ' is already defined as EmailConstants.BEARER, so use that everywhere

  // Instance variables
  @Value("${org.domain.name}")
  private List<String> orgDomainName;

  @Value("${total.meeting.limit}")
  private String meetingLimit;

  @Value("${outsiders.events.lookup.allow}")
  private boolean allowOutsiders;

  @Value("${graph.license.skuIds}")
  private List<String> licenseSkuIds;

  private final UserContextHolder userContextHolder;
  private final EmailUserDao emailUserDao;
  private final EmailPreferencesDao preferencesDao;
  private final TokenUtils tokenUtils;

  private static final String VALUE = "value";
  private static final String AVAILABILITY_VIEW = "availabilityView";
  private static final String STATUS = "status";
  private static final String MESSAGE = "message";
  private static final String FAILED = "failed";

  @Override
  public List<EventDto> getCalendarEventsV1(
          String email, String startDateTime, String endDateTime, String subject) {
    log.debug(
        "Inside @method getCalendarEventsV1. @param: email -> {} startDateTime -> {}, endDateTime -> {}",
        email,
        startDateTime,
        endDateTime);
    if (!EmailUtils.checkDomain(email, orgDomainName)) return new ArrayList<>();

    if (!allowOutsiders) {
      boolean userExists = emailUserDao.existsByEmailAndNotDeleted(email);
      if (!userExists) return new ArrayList<>();
    }

    EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);

    if (startDateTime != null && endDateTime != null && !startDateTime.endsWith("Z")) {
      startDateTime = DateUtils.convertToUTCString(startDateTime, preferences.getTimeZone());
      endDateTime = DateUtils.convertToUTCString(endDateTime, preferences.getTimeZone());
    }

    if (startDateTime == null || endDateTime == null) {
      startDateTime = DateUtils.getUTCDateTime(false, preferences.getTimeZone());
      endDateTime = DateUtils.getUTCDateTime(true, preferences.getTimeZone());
    }

      String url = null;
      try {
          URIBuilder uriBuilder =
              new URIBuilder(
                  String.format("https://graph.microsoft.com/v1.0/users/%s/calendarView", email));
          uriBuilder.addParameter("$top", meetingLimit);
          uriBuilder.addParameter(EmailConstants.STARTDATETIME, startDateTime);
          uriBuilder.addParameter(EmailConstants.ENDDATETIME, endDateTime);
          if (subject != null && !subject.isEmpty()) {
            uriBuilder.addParameter(
                "filter", String.format("contains(subject,'%s')", subject.toLowerCase()));
          }

          URI uri = uriBuilder.build();
          url = uri.toString();
      } catch (URISyntaxException e) {
          log.error("Error while building URI for getCalendarEventsV1: {}", e.getMessage(), e);
          throw new RuntimeException(e);
      }

      // Create an HttpClient instance
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      // Create a GET request
      HttpGet request = new HttpGet(url);
      request.setHeader(
          EmailConstants.AUTHORIZATION,
          EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
      request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

      // Execute the request
      try (CloseableHttpResponse response = httpClient.execute(request)) {
        int statusCode = response.getStatusLine().getStatusCode();

        // Extract the response entity
        HttpEntity entity = response.getEntity();
        if (entity != null && statusCode == HTTP_STATUS_OK) {
          // Convert the entity content to a String
          String responseBody = EntityUtils.toString(entity);

          return convertJsonToEventDto(responseBody);
        }
      }
    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      log.warn("Thread was interrupted while fetching calendar events", ie);
      throw new BusinessException("Thread interrupted while fetching calendar events", ie);
    } catch (Exception e) {
      log.error("Error while getting calendar events from graph : {}", e.getMessage(), e);
    }
    return new ArrayList<>();
  }


  @Override
  public Map<String, Object> updateEventV1(String eventId, String email, Map<String, Object> jsonBody) {
    String meetingRequestJson = CommonUtils.convertToMeetingRequestForUpdateEvent(jsonBody);
    return updateEvent(eventId, email, meetingRequestJson);
  }

  @Override
  public Map<String, Object> updateEvent(String eventId, String email, String meetingRequestJson) {
    log.debug("Inside @method updateEvent. @param: email -> {}, eventId -> {}, jsonBody -> {}",
        email, eventId, meetingRequestJson);

    String url = String.format(GRAPH_API_GET_EVENT, email, eventId);
    log.debug("URL to update event: {}", url);

    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

      // Step 1: Check if the event exists
      HttpGet getRequest = new HttpGet(url);
      getRequest.setHeader(
          EmailConstants.AUTHORIZATION,
          EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));

      try (CloseableHttpResponse getResponse = httpClient.execute(getRequest)) {
        int getStatusCode = getResponse.getStatusLine().getStatusCode();

        if (getStatusCode != HTTP_STATUS_OK) {
          // Event does not exist
          log.warn("Event with ID {} not found for user {}", eventId, email);
          return Map.of(EmailConstants.RESULT, EmailConstants.FAILED, EmailConstants.REASON, "Event not found");
        }
      }

      // Step 2: If exists, update the event
      HttpPatch patchRequest = new HttpPatch(url);
      patchRequest.setHeader(
          EmailConstants.AUTHORIZATION,
          EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
      patchRequest.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

      StringEntity entity =
          new StringEntity(
              meetingRequestJson, ContentType.APPLICATION_JSON.withCharset(StandardCharsets.UTF_8));
      patchRequest.setEntity(entity);

      try (CloseableHttpResponse patchResponse = httpClient.execute(patchRequest)) {
        int patchStatusCode = patchResponse.getStatusLine().getStatusCode();
        String responseBody =
            patchResponse.getEntity() != null
                ? EntityUtils.toString(patchResponse.getEntity())
                : "";

        if (patchStatusCode == HTTP_STATUS_OK) {
          JSONObject jsonObject = new JSONObject(responseBody);
          EventDto eventDto = getEventDto(jsonObject);

          Map<String, Object> map = new HashMap<>();
          map.put(EmailConstants.RESULT, EmailConstants.SUCCESS);
          map.put(EmailConstants.UPDATED_EVENT_OBJECT, new JSONObject(eventDto).toString());
          return map;
        } else {
          log.error("Error while updating event : {}", responseBody);
          return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
        }
      }

    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      log.warn("Thread was interrupted while update calendar event", ie);
      throw new BusinessException("Thread interrupted while update calendar event", ie);
    } catch (Exception e) {
      log.error("Exception while updating event : {}", e.getMessage(), e);
      return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
    }
  }

  @Override
  public Map<String, String> declineMeeting(String eventId) {
    Map<String, String> responseMap = new HashMap<>();
    try {
      log.debug("Inside @method declineMeeting. @param : eventId -> {}", eventId);
      String email = userContextHolder.getCurrentUser().getEmail();
      String declineUrl =
          String.format(
              "https://graph.microsoft.com/v1.0/users/%s/events/%s/decline", email, eventId);
      log.debug("URL to declineMeeting : {}", declineUrl);
      sendResponse(declineUrl, null);
      responseMap.put(EmailConstants.RESULT, EmailConstants.SUCCESS);
    } catch (Exception e) {
      log.error("Error inside @method decline meeting : {}", e.getMessage(), e);
      responseMap.put(EmailConstants.RESULT, EmailConstants.FAILED);
    }
    return responseMap;
  }

  @Override
  public Map<String, String> acceptMeeting(String eventId) {
    Map<String, String> responseMap = new HashMap<>();
    try {
      log.debug("Inside @method acceptMeeting. @param : eventId -> {}", eventId);
      String email = userContextHolder.getCurrentUser().getEmail();
      String acceptUrl =
          String.format(
              "https://graph.microsoft.com/v1.0/users/%s/events/%s/accept", email, eventId);
      log.debug("URL to acceptMeeting : {}", acceptUrl);
      sendResponse(acceptUrl, null);

      responseMap.put(EmailConstants.RESULT, EmailConstants.SUCCESS);

    } catch (Exception e) {
      log.error("Error inside @method accept meeting : {}", e.getMessage(), e);
      responseMap.put(EmailConstants.RESULT, EmailConstants.FAILED);
    }
    return responseMap;
  }

  @Override
  public Map<String, String> tentativelyAccept(String eventId) {
    Map<String, String> responseMap = new HashMap<>();
    try {
      String email = userContextHolder.getCurrentUser().getEmail();
      String url =
          String.format(
              "https://graph.microsoft.com/v1.0/users/%s/events/%s/tentativelyAccept",
              email, eventId);
      log.debug("URL to tentativelyAccept : {}", url);

      String proposedNewTime =
          "{\"comment\": \"Meeting is tentatively accepted.\",\"sendResponse\":true}";

      sendResponse(url, proposedNewTime);
      responseMap.put(EmailConstants.RESULT, EmailConstants.SUCCESS);
    } catch (Exception e) {
      log.error("Error inside @method decline meeting : {}", e.getMessage(), e);
      responseMap.put(EmailConstants.RESULT, EmailConstants.FAILED);
    }
    return responseMap;
  }

  private void sendResponse(String url, String proposedNewTime) throws Exception {
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      HttpPost request = new HttpPost(url);
      request.setHeader(
          EmailConstants.AUTHORIZATION,
          EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
      request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

      if (proposedNewTime != null) {
        StringEntity entity = new StringEntity(proposedNewTime);
        request.setEntity(entity);
      }

      try (CloseableHttpResponse response = httpClient.execute(request)) {
        if (response.getStatusLine().getStatusCode() == HTTP_STATUS_ACCEPTED) {
          log.debug("Meeting response sent successfully.");
        } else {
          log.error(
              "Failed to send meeting response. Status code: {}",
              response.getStatusLine().getStatusCode());
          throw new BusinessException(EntityUtils.toString(response.getEntity()));
        }
      }
    }
  }

  @Override
  public Map<String, Object> scheduleEvent(String email, Map<String, Object>  jsonBody) {
    log.debug("Inside @method scheduleEvent. @param: email -> {}, jsonBody -> {}", email, jsonBody);

    String url = String.format("https://graph.microsoft.com/v1.0/users/%s/events", email);
    log.debug("URL to scheduleEvent : {}", url);

    // Create an HttpClient instance
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      // Create a PATCH request
      HttpPost request = new HttpPost(url);
      request.setHeader(
          EmailConstants.AUTHORIZATION,
          EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
      request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

      StringEntity entity =
          new StringEntity(
                  jsonBody.toString(), ContentType.APPLICATION_JSON.withCharset(StandardCharsets.UTF_8));

      request.setEntity(entity);

      // Execute the request
      try (CloseableHttpResponse response = httpClient.execute(request)) {
        if (response.getStatusLine().getStatusCode() == HTTP_STATUS_CREATED) {
          String responseBody = EntityUtils.toString(response.getEntity());
          JSONObject jsonObject = new JSONObject(responseBody);
          EventDto eventDto = getEventDto(jsonObject);
          Map<String, Object> map = new HashMap<>();
          map.put(EmailConstants.RESULT, EmailConstants.SUCCESS);
          map.put(EmailConstants.SCHEDULE_EVENT_OBJECT, new JSONObject(eventDto).toString());
          return map;
        }
        String errorMessage = EntityUtils.toString(response.getEntity());
        log.error("Error while scheduling event : {}", errorMessage);
        return Map.of(EmailConstants.RESULT, EmailConstants.FAILED,
                      EmailConstants.ERROR, errorMessage);
      }
    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      log.warn("Thread was interrupted while schedule calendar event", ie);
      throw new BusinessException("Thread interrupted while schedule calendar event", ie);
    } catch (Exception e) {
      log.error("Error while scheduling event : {}", e.getMessage(), e);
      return Map.of(EmailConstants.RESULT, EmailConstants.FAILED,
              EmailConstants.ERROR, e.getMessage());
    }
  }

  @Override
  public Map<String, String> rescheduleEvent(Map<String, String> requestBody) {
    log.debug("Inside @method rescheduleEvent. @param : requestBody -> {}", requestBody);

    String email = userContextHolder.getCurrentUser().getEmail();
    String timeZone = requestBody.get(EmailConstants.TIME_ZONE);
    if (timeZone == null) {
      EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
      timeZone = preferences.getTimeZone();
    }
    requestBody.put(EmailConstants.TIME_ZONE, timeZone);

    String eventId = requestBody.get("eventId");
    String url = String.format(GRAPH_API_GET_EVENT, email, eventId);
    log.debug("URL to rescheduleEvent : {}", url);

    // Create an HttpClient instance
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      // Create a PATCH request
      HttpPatch request = new HttpPatch(url);
      request.setHeader(
          EmailConstants.AUTHORIZATION,
          EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
      request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

      String payload = CommonUtils.convertToRescheduleMeetingPayload(requestBody);
      StringEntity entity = new StringEntity(payload);
      request.setEntity(entity);

      // Execute the request
      try (CloseableHttpResponse response = httpClient.execute(request)) {
        int statusCode = response.getStatusLine().getStatusCode();

        if (statusCode == HTTP_STATUS_OK) {
          return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
        }
        String errorMessage = EntityUtils.toString(response.getEntity());
        log.error("Error while rescheduling event : {}", errorMessage);
        return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
      }
    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      log.warn("Thread was interrupted while re-schedule calendar event", ie);
      throw new BusinessException("Thread interrupted while re-schedule calendar event", ie);
    } catch (Exception e) {
      log.error("Error while rescheduling event : {}", e.getMessage(), e);
      return Map.of(EmailConstants.RESULT, EmailConstants.FAILED);
    }
  }

  @Override
  public List<EventDto> getEventDetailsBySubjectAndTime(
          String email, String subject, String startDateTime, String endDateTime) {
    log.debug(
        "Fetching event details for subject: {}, start: {}, end: {}",
        subject,
        startDateTime,
        endDateTime);

    try {
      // Build the URI to search for the event in the user's calendar using subject and time filter
      URIBuilder uriBuilder =
          new URIBuilder(
              String.format("https://graph.microsoft.com/v1.0/users/%s/calendar/events", email));

      // Add filters for time, subject, and location
      uriBuilder.addParameter(
          "$filter",
          String.format(
              "(start/dateTime ge '%s' and end/dateTime le '%s' and subject eq '%s')",
              startDateTime, endDateTime, subject));
      URI uri = uriBuilder.build();
      String url = uri.toString();
      log.debug("URL to fetch event details: {}", url);

      // Create an HttpClient instance and send a GET request
      try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
        HttpGet request = new HttpGet(url);
        request.setHeader(
            EmailConstants.AUTHORIZATION,
            EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
        request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

        // Execute the request
        try (CloseableHttpResponse response = httpClient.execute(request)) {
          HttpEntity entity = response.getEntity();
          if (entity != null && response.getStatusLine().getStatusCode() == HTTP_STATUS_OK) {
            String responseBody = EntityUtils.toString(entity);
            return convertJsonToEventDto(responseBody); // Convert response to EventDto list
          }
        }
      }
    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      log.warn("Thread was interrupted while fetching calendar event", ie);
      throw new BusinessException("Thread interrupted while fetching calendar event", ie);
    } catch (Exception e) {
      log.error("Error while fetching event details", e);
      throw new BusinessException(
          String.format("Unable to fetch event details for subject: %s", subject));
    }
    return new ArrayList<>();
  }

    @Override
    public List<EventDto> getEventDetails(String email, String eventId, String startDateTime, String endDateTime) {
        log.debug(
                "Fetching event details for eventId: {}, start: {}, end: {}",
                eventId,
                startDateTime,
                endDateTime);

        try {
            // Build the URI to search for the event in the user's calendar using subject and time filter
            URIBuilder uriBuilder =
                    new URIBuilder(String.format("https://graph.microsoft.com/v1.0/users/%s/calendar/events/%s/instances?startDateTime=%s&endDateTime=%s",
                                    email, eventId, startDateTime, endDateTime));


            URI uri = uriBuilder.build();
            String url = uri.toString();
            log.debug("URL to fetch event details : {}", url);

            // Create an HttpClient instance and send a GET request
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                HttpGet request = new HttpGet(url);
                request.setHeader(
                        EmailConstants.AUTHORIZATION,
                        EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
                request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

                // Execute the request
                try (CloseableHttpResponse response = httpClient.execute(request)) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null && response.getStatusLine().getStatusCode() == HTTP_STATUS_OK) {
                        String responseBody = EntityUtils.toString(entity);
                        return convertJsonToEventDto(responseBody); // Convert response to EventDto list
                    }
                }
            }
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            log.warn("Thread was interrupted while fetching calendar event", ie);
            throw new BusinessException("Thread interrupted while fetching calendar event", ie);
        } catch (Exception e) {
            log.error("Error while fetching event details", e);
            throw new BusinessException(
                    String.format("Unable to fetch event details for eventId: %s", eventId));
        }
        return new ArrayList<>();
    }

  @Override
  public AvailableSlots getAvailableSlotsAndConflictV1(
          List<String> emails,
          String startDateTime,
          String endDateTime,
          int slotDuration,
          Boolean workingFlag)
      throws Exception {
    log.debug(
        "Inside @method getAvailableSlotsAndConflictV1. @param: emails -> {}, startDateTime -> {}, endDateTime -> {} workingFlag -> {}",
        emails,
        startDateTime,
        endDateTime,
        workingFlag);
    String[] dateTimeRange = setupDateTimeRange(startDateTime, endDateTime);
    startDateTime = dateTimeRange[0];
    endDateTime = dateTimeRange[1];
    AvailableSlots availableSlotsAndConflict = new AvailableSlots();

    String email = userContextHolder.getCurrentUser().getEmail();
    String timeZone = "UTC";

    // Prepare request body
    JSONObject requestBody = new JSONObject();
    requestBody.put(EmailConstants.SCHEDULES, emails);

    JSONObject startTime = new JSONObject();
    startTime.put(EmailConstants.DATE_TIME, startDateTime);
    startTime.put(EmailConstants.TIME_ZONE, timeZone);
    requestBody.put(EmailConstants.START_TIME, startTime);

    JSONObject endTime = new JSONObject();
    endTime.put(EmailConstants.DATE_TIME, endDateTime);
    endTime.put(EmailConstants.TIME_ZONE, timeZone);
    requestBody.put(EmailConstants.END_TIME, endTime);

    requestBody.put(EmailConstants.AVAILABILITY_VIEW_INTERVAL, slotDuration);

    // Make API call
    String url = GRAPH_API_USERS_BASE_URL + email + CALENDAR_GET_SCHEDULE_PATH;
    String response = executePostRequest(url, requestBody.toString());

    // Parse response
    List<JSONObject> userWorkingHoursList = getUserWorkingHoursList(response);

    List<Meeting> availableTimeSlots = getAvailableTimeSlots(emails, startDateTime, slotDuration);
    WorkingHoursSummary workingHoursSummary =
        WorkingHoursUtil.getWorkingHoursSummary(userWorkingHoursList);
    List<Meeting> meetingList = filterMeetingSlots(availableTimeSlots, workingHoursSummary);
    if (workingFlag != null && !workingFlag && meetingList.isEmpty()) {
      log.debug("setting all the meetings without filter");
      availableSlotsAndConflict.setSlots(availableTimeSlots);
      availableSlotsAndConflict.setFiltered(false);
    } else {
      log.debug("setting all the meetings with filter");
      availableSlotsAndConflict.setSlots(meetingList);
      availableSlotsAndConflict.setFiltered(true);
    }
    return availableSlotsAndConflict;
  }

  @NotNull
  private static List<JSONObject> getUserWorkingHoursList(String response) {
    JSONObject jsonResponse = new JSONObject(response);
    JSONArray scheduleInfoArray = jsonResponse.getJSONArray(VALUE);

    // Create response
    List<JSONObject> userWorkingHoursList = new ArrayList<>();
    // Process each schedule info to get availabilityView
    for (int i = 0; i < scheduleInfoArray.length(); i++) {
      JSONObject scheduleInfo = scheduleInfoArray.getJSONObject(i);
      if (!scheduleInfo.has(AVAILABILITY_VIEW)) {
        continue;
      }
      JSONObject userWorkingHours = scheduleInfo.getJSONObject(EmailConstants.WORKING_HOURS);
      userWorkingHoursList.add(userWorkingHours);
    }
    return userWorkingHoursList;
  }

  @Override
  public String getSchedule(
          List<String> emails,
          String startDateTime,
          String endDateTime,
          Integer slotDuration,
          Boolean workingFlag) {
    try {
      log.debug(
          "Inside @method getSchedule. @param: emails -> {}, startDateTime -> {}, endDateTime -> {} workingFlag -> {} slotDuration -> {}",
          emails,
          startDateTime,
          endDateTime,
          workingFlag,
          slotDuration);

      // Get timezone from preferences
      String email = userContextHolder.getCurrentUser().getEmail();
      String timeZone = "UTC";

      // Prepare request body
      JSONObject requestBody = new JSONObject();
      requestBody.put(EmailConstants.SCHEDULES, emails);

      JSONObject startTime = new JSONObject();
      startTime.put(EmailConstants.DATE_TIME, startDateTime);
      startTime.put(EmailConstants.TIME_ZONE, timeZone);
      requestBody.put(EmailConstants.START_TIME, startTime);

      JSONObject endTime = new JSONObject();
      endTime.put(EmailConstants.DATE_TIME, endDateTime);
      endTime.put(EmailConstants.TIME_ZONE, timeZone);
      requestBody.put(EmailConstants.END_TIME, endTime);

      requestBody.put(EmailConstants.AVAILABILITY_VIEW_INTERVAL, slotDuration);

      // Make API call
      String url = GRAPH_API_USERS_BASE_URL + email + CALENDAR_GET_SCHEDULE_PATH;
      String response = executePostRequest(url, requestBody.toString());
      log.debug("Successfully fetched schedule for emails: {}", emails);
      return response;
    } catch (Exception e) {
      log.error("Error while fetching schedule for emails: {}", emails, e);
      throw new BusinessException("Error while fetching schedule");
    }
  }

  private String[] setupDateTimeRange(String startDateTime, String endDateTime) {
    if (startDateTime == null || endDateTime == null) {
      String email = userContextHolder.getCurrentUser().getEmail();
      EmailUser user = emailUserDao.findByEmail(email);
      String userId = user.getEmail();
      EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
      startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
      endDateTime =
          DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
    }
    return new String[] {startDateTime, endDateTime};
  }

  private static <T> List<T> firstN(List<T> list, int n) {
    if (list == null || list.isEmpty()) {
      return List.of(); // or Collections.emptyList()
    }
    return list.subList(0, Math.min(n, list.size()));
  }

  @Override
  public AvailabilityResponse getAvailability(
          List<String> emails,
          String startDateTime,
          String endDateTime,
          int slotDuration,
          Boolean workingFlag)
      throws Exception {
    log.debug(
        "Inside @method getAvailability. @param: emails -> {}, startDateTime -> {}, endDateTime -> {} workingFlag -> {}",
        emails,
        startDateTime,
        endDateTime,
        workingFlag);

    // Get timezone from preferences
    String email = userContextHolder.getCurrentUser().getEmail();
    String timeZone = "UTC";

    // Prepare request body
    JSONObject requestBody = new JSONObject();
    requestBody.put(EmailConstants.SCHEDULES, emails);

    JSONObject startTime = new JSONObject();
    startTime.put(EmailConstants.DATE_TIME, startDateTime);
    startTime.put(EmailConstants.TIME_ZONE, timeZone);
    requestBody.put(EmailConstants.START_TIME, startTime);

    JSONObject endTime = new JSONObject();
    endTime.put(EmailConstants.DATE_TIME, endDateTime);
    endTime.put(EmailConstants.TIME_ZONE, timeZone);
    requestBody.put(EmailConstants.END_TIME, endTime);

    requestBody.put(EmailConstants.AVAILABILITY_VIEW_INTERVAL, slotDuration);

    // Make API call
    String url = GRAPH_API_USERS_BASE_URL + email + CALENDAR_GET_SCHEDULE_PATH;
    String response = executePostRequest(url, requestBody.toString());

    // Parse response
    JSONObject jsonResponse = new JSONObject(response);
    JSONArray scheduleInfoArray = jsonResponse.getJSONArray(VALUE);

    // Create response
    List<TimeSlot> slots = new ArrayList<>();
    Map<String, String> availabilityMap = new HashMap<>();
    Map<String, JSONObject> workingHoursMap = new HashMap<>();
    Object organizerMeeting = null;
    List<JSONObject> userWorkingHoursList = new ArrayList<>();
    // Process each schedule info to get availabilityView
    for (int i = 0; i < scheduleInfoArray.length(); i++) {
      JSONObject scheduleInfo = scheduleInfoArray.getJSONObject(i);
      String scheduleId = scheduleInfo.getString("scheduleId");
      if (!scheduleInfo.has(AVAILABILITY_VIEW)) {
        continue;
      }
      String availabilityView = scheduleInfo.getString(AVAILABILITY_VIEW);
      JSONObject userWorkingHours = scheduleInfo.getJSONObject(EmailConstants.WORKING_HOURS);
      userWorkingHoursList.add(userWorkingHours);

      if (scheduleId.equalsIgnoreCase(email)
          && ("1".equals(availabilityView) || "2".equals(availabilityView))) {
        JSONArray scheduleItems = scheduleInfo.getJSONArray("scheduleItems");
        if (!scheduleItems.isEmpty()) organizerMeeting = scheduleItems.toString();
      }

      availabilityMap.put(scheduleId, availabilityView);
      workingHoursMap.put(scheduleId, userWorkingHours);
    }

    // Convert availability view to time slots
    Date start = convertStringToDate(startDateTime);
    Calendar cal = Calendar.getInstance();
    cal.setTime(start);

    // For each character in the availabilityView string
    String firstAvailabilityView = availabilityMap.values().iterator().next();
    for (int i = 0; i < firstAvailabilityView.length(); i++) {
      Date slotStart = cal.getTime();
      cal.add(Calendar.MINUTE, slotDuration);
      Date slotEnd = cal.getTime();

      List<AvailabilityStatus> availability = new ArrayList<>();
      for (String userEmail : emails) {
        String availabilityView = availabilityMap.get(userEmail);
        if (availabilityView != null && i < availabilityView.length()) {
          char status = availabilityView.charAt(i);
          String availabilityStatus =
              switch (status) {
                case '0' -> "free";
                case '1' -> "tentative";
                case '2' -> "busy";
                case '3' -> "outOfOffice";
                case '4' -> "workingElsewhere";
                default -> "unknown";
              };
          // Check for away
          if (workingFlag != null && workingFlag) {
            JSONObject workingHoursObject = workingHoursMap.get(userEmail);
            String startTime1 =
                workingHoursObject.getString(EmailConstants.START_TIME); // e.g., "08:00:00.0000000"
            String endTime1 = workingHoursObject.getString(EmailConstants.END_TIME); // e.g., "17:00:00.0000000"
            String timeZone1 = workingHoursObject.getJSONObject(EmailConstants.TIME_ZONE).getString("name");
            String ianaTimezone = GRAPH_API_TO_IANA_TIMEZONE.getOrDefault(timeZone1, "UTC");

            ZoneId userZoneId = ZoneId.of(ianaTimezone);
            ZonedDateTime slotStartZoned = slotStart.toInstant().atZone(userZoneId);
            ZonedDateTime slotEndZoned = slotEnd.toInstant().atZone(userZoneId);

            // Extract working hours
            LocalTime workStartTime = LocalTime.parse(startTime1.substring(0, TIME_STRING_LENGTH)); // "08:00:00"
            LocalTime workEndTime = LocalTime.parse(endTime1.substring(0, TIME_STRING_LENGTH)); // "17:00:00"
            DayOfWeek slotDay = slotStartZoned.getDayOfWeek();

            // Check if the day is a working day
            JSONArray workingDays = workingHoursObject.getJSONArray("daysOfWeek");
            boolean isWorkingDay = false;
            for (int d = 0; d < workingDays.length(); d++) {
              if (workingDays.getString(d).equalsIgnoreCase(slotDay.name().toLowerCase())) {
                isWorkingDay = true;
                break;
              }
            }

            // Check if slot is within working hours
            boolean isWithinWorkingHours =
                isWorkingDay
                    && !slotStartZoned.toLocalTime().isBefore(workStartTime)
                    && !slotEndZoned.toLocalTime().isAfter(workEndTime);

            // if slot is outside working hours, then set the status to away
            if (!isWithinWorkingHours) {
              availabilityStatus = "away";
            }
          }

          if (userEmail.equalsIgnoreCase(email)) {
            availability.add(
                new AvailabilityStatus(userEmail, availabilityStatus, organizerMeeting));
          } else {
            availability.add(new AvailabilityStatus(userEmail, availabilityStatus, null));
          }
        }
      }

      slots.add(new TimeSlot(slotStart, slotEnd, availability));
    }

    WorkingHoursSummary workingHoursSummary =
        WorkingHoursUtil.getWorkingHoursSummary(userWorkingHoursList);

    List<Meeting> availableSlots = getAvailableTimeSlots(emails, startDateTime, slotDuration);
    List<Meeting> meetingList = filterMeetingSlots(availableSlots, workingHoursSummary);

    List<Meeting> finalMeetingList = firstN(meetingList, MAX_MEETING_RESULTS);
    return new AvailabilityResponse(slots, finalMeetingList);
  }

  private List<Meeting> getAvailableTimeSlots(
      List<String> emails, String startDateTime, int slotDuration) throws Exception {
    Date startDate = convertStringToDate(startDateTime);

    LocalDateTime localDateTime = DateUtils.convertToLocalDateTime(startDateTime);
    localDateTime = DateUtils.roundOffTime(localDateTime);

    LocalDateTime plus7Days = localDateTime.plusDays(DAYS_AHEAD);
    String date7DaysAhead = DateUtils.convertToUtcIsoString(plus7Days);
    Date end7DaysAhead = convertStringToDate(date7DaysAhead);
    Map<String, List<Meeting>> resultForNext7Days =
        fetchCalendarEventsForEmailsV1(emails, startDateTime, date7DaysAhead, slotDuration);
    return findAvailableTimeSlots(
        resultForNext7Days, startDate, end7DaysAhead, slotDuration * MINUTES_PER_HOUR * MILLIS_PER_SECOND);
  }

  private Map<String, List<Meeting>> fetchCalendarEventsForEmailsV1(
      List<String> emails, String startDateTime, String endDateTime, int slotDuration)
      throws Exception {
    Map<String, List<Meeting>> result = new HashMap<>();

    // Get timezone from preferences
    String email = userContextHolder.getCurrentUser().getEmail();
    EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
    String timeZone = preferences.getTimeZone();

    // Prepare request body
    JSONObject requestBody = new JSONObject();
    requestBody.put(EmailConstants.SCHEDULES, emails);

    JSONObject startTimeObj = new JSONObject();
    startTimeObj.put(EmailConstants.DATE_TIME, startDateTime);
    startTimeObj.put(EmailConstants.TIME_ZONE, timeZone);
    requestBody.put(EmailConstants.START_TIME, startTimeObj);

    JSONObject endTimeObj = new JSONObject();
    endTimeObj.put(EmailConstants.DATE_TIME, endDateTime);
    endTimeObj.put(EmailConstants.TIME_ZONE, timeZone);
    requestBody.put(EmailConstants.END_TIME, endTimeObj);

    requestBody.put(EmailConstants.AVAILABILITY_VIEW_INTERVAL, slotDuration);

    // Make API call
    String url = GRAPH_API_USERS_BASE_URL + email + CALENDAR_GET_SCHEDULE_PATH;
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      HttpPost httpPost = new HttpPost(url);
      httpPost.setHeader(
          EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
      httpPost.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);
      httpPost.setHeader("Prefer", "outlook.timezone=\"" + timeZone + "\"");
      httpPost.setEntity(new StringEntity(requestBody.toString()));

      try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
        String responseBody = EntityUtils.toString(response.getEntity());
        JSONObject jsonResponse = new JSONObject(responseBody);
        JSONArray scheduleInfoArray = jsonResponse.getJSONArray(VALUE);

        for (int i = 0; i < scheduleInfoArray.length(); i++) {
          JSONObject scheduleInfo = scheduleInfoArray.getJSONObject(i);
          String scheduleId = scheduleInfo.getString("scheduleId");
          List<Meeting> meetings = new ArrayList<>();

          // Use availabilityView if present
          if (scheduleInfo.has(AVAILABILITY_VIEW)) {
            String availabilityView = scheduleInfo.getString(AVAILABILITY_VIEW);
            // Parse start time as UTC ISO string
            Instant instant = Instant.parse(startDateTime);
            ZoneId zoneId = ZoneId.of(timeZone);
            ZonedDateTime slotStart = instant.atZone(zoneId);
            for (int idx = 0; idx < availabilityView.length(); idx++) {
              char c = availabilityView.charAt(idx);
              if (c == '2') { // busy
                ZonedDateTime slotStartTime =
                    slotStart.plusMinutes((long) idx * slotDuration);
                ZonedDateTime slotEndTime = slotStartTime.plusMinutes(slotDuration);
                Date start = Date.from(slotStartTime.toInstant());
                Date end = Date.from(slotEndTime.toInstant());
                Meeting meeting = new Meeting();
                meeting.setStartTime(start);
                meeting.setEndTime(end);
                meetings.add(meeting);
              }
            }
          }
          result.put(scheduleId, meetings);
        }
      }
    }
    return result;
  }

  private String executePostRequest(String url, String requestBody) throws Exception {
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      HttpPost request = new HttpPost(url);
      request.setHeader(
          EmailConstants.AUTHORIZATION,
          EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
      request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);
      request.setHeader(EmailConstants.PREFER, "outlook.timezone=\"" + "UTC" + "\"");
      request.setEntity(new StringEntity(requestBody));

      try (CloseableHttpResponse response = httpClient.execute(request)) {
        int statusCode = response.getStatusLine().getStatusCode();
        HttpEntity entity = response.getEntity();
        String apiResponse = EntityUtils.toString(entity);
        if (statusCode == HTTP_STATUS_OK) {
          return apiResponse;
        } else {
          log.error("error from /getSchedule : {}", apiResponse);
        }
        throw new ApiException(
            "Failed to get availability. Status code: " + statusCode + " Message: " + apiResponse);
      }
    }
  }

  private List<Meeting> filterMeetingSlots(
      List<Meeting> meetingList, WorkingHoursSummary workingHoursSummary) {
    List<TimeSlot> timeSlots =
        meetingList.stream()
            .map(meeting -> new TimeSlot(meeting.getStartTime(), meeting.getEndTime(), null))
            .toList();

    List<TimeSlot> timeSlots1 = filterSlots(timeSlots, workingHoursSummary);

    return timeSlots1.stream()
        .map(timeSlot -> new Meeting(timeSlot.getStartTime(), timeSlot.getEndTime()))
        .toList();
  }

  private List<TimeSlot> filterSlots(List<TimeSlot> slots, WorkingHoursSummary summary) {
    if (summary == null || summary.getOverlappingTimeRangesUTC().isEmpty()) return List.of();

    return slots.stream()
        .filter(
            (TimeSlot slot) -> {
              ZonedDateTime zdt = slot.getStartTime().toInstant().atZone(ZoneOffset.UTC);
              String day = zdt.getDayOfWeek().name().toLowerCase();
              int minutes = zdt.getHour() * MINUTES_PER_HOUR + zdt.getMinute();

              List<WorkingHoursSummary.TimeRange> dayRanges =
                  summary.getOverlappingTimeRangesUTC().get(day);
              if (dayRanges == null) return false;

              for (WorkingHoursSummary.TimeRange range : dayRanges) {
                if (minutes >= range.getStartMinutesUTC() && minutes < range.getEndMinutesUTC()) {
                  return true;
                }
              }
              return false;
            })
        .toList();
  }

  @Override
  public Map<String, String> cancelEvent(String eventId, String comment) throws Exception {
    String emailId = userContextHolder.getCurrentUser().getEmail();
    String url = String.format(GRAPH_API_CANCEL_EVENT, emailId, eventId);
    log.info("Attempting to cancel event for user: {}, eventId: {}", emailId, eventId);

    JSONObject requestBody = new JSONObject();
    requestBody.put(EmailConstants.COMMENT, comment);

    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      HttpPost httpPost = new HttpPost(url);
      httpPost.setHeader(EmailConstants.CONTENT_TYPE, "application/json");
      httpPost.setHeader(
          EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));

      StringEntity entity =
          new StringEntity(
              requestBody.toString(),
              ContentType.APPLICATION_JSON.withCharset(StandardCharsets.UTF_8));

      httpPost.setEntity(entity);

      try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
        int statusCode = response.getStatusLine().getStatusCode();
        Map<String, String> result = new HashMap<>();
        String responseBody = EntityUtils.toString(response.getEntity());
        log.debug("Received response with status code: {} and body: {}", statusCode, responseBody);

        if (statusCode == HTTP_STATUS_ACCEPTED) {
          result.put(STATUS, "success");
          result.put(MESSAGE, "Event cancelled successfully");
          log.info("Successfully cancelled event for user: {}, eventId: {}", emailId, eventId);
        } else {
          // Event not found or already cancelled
          result.put(STATUS, FAILED);
          JSONObject errorJson = new JSONObject(responseBody);
          String errorMessage = errorJson.getJSONObject(EmailConstants.ERROR).getString(MESSAGE);
          result.put(MESSAGE, errorMessage);
          log.warn(
              "Failed to cancel event for user: {}, eventId: {}, error: {}",
              emailId,
              eventId,
              errorMessage);
        }
        return result;
      }
    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      log.warn("Thread was interrupted while canceling calendar event", ie);
      throw new BusinessException("Thread interrupted while canceling calendar event", ie);
    } catch (Exception e) {
      log.error(
          "Error occurred while cancelling event for user: {}, eventId: {}, error: {}",
          emailId,
          eventId,
          e.getMessage(),
          e);
      throw e;
    }
  }

  @Override
  public Map<String, String> forwardEvent(String eventId, List<String> emailIds, String comment)
      throws IOException, InterruptedException {
    log.debug("Inside @method forwardEvent. eventId: {}, emailIds: {}", eventId, emailIds);
    if (eventId == null || emailIds == null || emailIds.isEmpty()) {
      log.error("Required parameters missing. eventId: {}, emailIds: {}", eventId, emailIds);
      return Map.of(
          EmailConstants.STATUS,
          EmailConstants.FAILED,
          EmailConstants.MESSAGE,
          "Event ID and at least one email ID are required");
    }

    String emailId = userContextHolder.getCurrentUser().getEmail();
    try {
      String url = String.format(GRAPH_API_FORWARD_EVENT, emailId, eventId);

      JSONObject requestBody = new JSONObject();
      if (comment != null && !comment.trim().isEmpty()) {
        requestBody.put(EmailConstants.COMMENT, comment);
      }

      JSONArray recipients = new JSONArray();
      for (String email : emailIds) {
        JSONObject recipientObj = new JSONObject();
        JSONObject emailAddressObj = new JSONObject();
        emailAddressObj.put(EmailConstants.ADDRESS, email);
        recipientObj.put("EmailAddress", emailAddressObj);
        recipients.put(recipientObj);
      }
      requestBody.put(EmailConstants.TO_RECIPIENTS, recipients);

      log.debug(
          "Sending forward request to Graph API. URL: {}, Request body: {}", url, requestBody);

      try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader(
            EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
        httpPost.setHeader(EmailConstants.CONTENT_TYPE, "application/json");

        StringEntity entity =
            new StringEntity(
                requestBody.toString(),
                ContentType.APPLICATION_JSON.withCharset(StandardCharsets.UTF_8));

        httpPost.setEntity(entity);

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
          int statusCode = response.getStatusLine().getStatusCode();
          Map<String, String> result = new HashMap<>();
          String responseBody = EntityUtils.toString(response.getEntity());
          log.debug(
              "Received response with status code: {} and body: {}", statusCode, responseBody);

          if (statusCode == HTTP_STATUS_ACCEPTED) {
            result.put(STATUS, "success");
            result.put(MESSAGE, "Event forwarded successfully");
            log.info("Successfully forwarded event for user: {}, eventId: {}", emailId, eventId);
          } else {
            result.put(STATUS, FAILED);
            JSONObject errorJson = new JSONObject(responseBody);
            String errorMessage = errorJson.getJSONObject(EmailConstants.ERROR).getString(MESSAGE);
            result.put(MESSAGE, errorMessage);
            log.warn(
                "Failed to forward event for user: {}, eventId: {}, error: {}",
                emailId,
                eventId,
                errorMessage);
          }
          return result;
        }
      }
    } catch (Exception e) {
      log.error(
          "Error occurred while forwarding event for user: {}, eventId: {}, error: {}",
          emailId,
          eventId,
          e.getMessage(),
          e);
      throw e;
    }
  }

  @Override
  public EventDto getCalendarEventByEventId(String eventId) throws Exception {
    String userEmail = userContextHolder.getCurrentUser().getEmail();
    if (userEmail == null || userEmail.isEmpty()) {
      throw new RuntimeException("User email not found in context");
    }

    String url = String.format(GRAPH_API_GET_EVENT, userEmail, eventId);
    log.debug("URL to get calendar event: {}", url);

    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      HttpGet request = new HttpGet(url);
      request.setHeader(
          EmailConstants.AUTHORIZATION,
          EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
      request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

      try (CloseableHttpResponse response = httpClient.execute(request)) {
        int statusCode = response.getStatusLine().getStatusCode();
        HttpEntity entity = response.getEntity();

        if (entity != null && statusCode == HTTP_STATUS_OK) {
          String responseBody = EntityUtils.toString(entity);
          JSONObject jsonObject = new JSONObject(responseBody);
          return getEventDto(jsonObject);
        } else {
          String error = entity != null ? EntityUtils.toString(entity) : "No response body";
          log.error(
              "Error fetching calendar event. Status code: {}, Response: {}", statusCode, error);
          throw new BusinessException(error);
        }
      }
    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      log.warn("Thread was interrupted while fetching calendar event by id", ie);
      throw new BusinessException("Thread interrupted while fetching calendar event by id", ie);
    } catch (Exception e) {
      log.error("Error in getCalendarEventByEventId: {}", e.getMessage(), e);
      throw new BusinessException(e.getMessage());
    }
  }

  private List<EventDto> convertJsonToEventDto(String jsonString) {
    JSONObject jsonObject = new JSONObject(jsonString);

    JSONArray messages = jsonObject.getJSONArray(EmailConstants.VALUE);

    List<EventDto> resultList = new ArrayList<>();

    for (int i = 0; i < messages.length(); i++) {
      JSONObject message = messages.getJSONObject(i);

      EventDto eventDto = getEventDto(message);

      resultList.add(eventDto);
    }
    return resultList;
  }

  private EventDto getEventDto(JSONObject message) {
    String id = message.getString("id");
    String organizer = null;
    if (message
        .getJSONObject("organizer")
        .getJSONObject(EmailConstants.EMAIL_ADDRESS)
        .has(EmailConstants.ADDRESS)) {
      organizer =
          message
              .getJSONObject("organizer")
              .getJSONObject(EmailConstants.EMAIL_ADDRESS)
              .getString(EmailConstants.ADDRESS);
    }
    List<String> attendees = getRecipients(message, "attendees");
    List<AttendeeAndStatus> required = getAttendeesByType(message, "required");
    List<AttendeeAndStatus> optional = getAttendeesByType(message, "optional");
    String location = message.getJSONObject("location").getString("displayName");
    String subject = null;
    try {
      subject = message.getString(EmailConstants.SUBJECT);
    } catch (JSONException e) {
      subject = "";
    }
    JSONObject body = message.getJSONObject("body");
    String bodyPreview = body.getString(EmailConstants.CONTENT);
    String joinUrl = null;
    if (message.getBoolean("isOnlineMeeting")) {
      joinUrl = message.getJSONObject("onlineMeeting").getString("joinUrl");
    }
    Boolean hasAttachments = message.getBoolean("hasAttachments");
    Boolean isCancelled = message.getBoolean("isCancelled");

    String seriesMasterId = message.optString("seriesMasterId");
    String type = message.optString("type");
    String occurrenceId = message.optString("occurrenceId");

    Date meetingStartTime = getDateFromJSONObject(message, "start");
    Date meetingEndTime = getDateFromJSONObject(message, "end");
    // Assuming 'createdDateTime' is in ISO 8601 format
    Date createdDateTime = Date.from(Instant.parse(message.getString("createdDateTime")));
    Date lastModifiedDateTime = Date.from(Instant.parse(message.getString("lastModifiedDateTime")));
    String accepted = message.getJSONObject("responseStatus").getString("response");
    JSONObject recurrence = message.optJSONObject("recurrence");
    String recurrenceString = recurrence != null ? recurrence.toString() : null;

    EventDto eventDto = new EventDto();
    eventDto.setId(id);
    eventDto.setOrganizer(organizer);
    eventDto.setAttendees(attendees);
    eventDto.setSubject(subject);
    eventDto.setAccepted(accepted);
    eventDto.setBodyPreview(bodyPreview);
    eventDto.setJoinUrl(joinUrl);
    eventDto.setHasAttachments(hasAttachments);
    eventDto.setIsCancelled(isCancelled);
    eventDto.setMeetingStartTime(meetingStartTime);
    eventDto.setMeetingEndTime(meetingEndTime);
    eventDto.setCreatedDateTime(createdDateTime);
    eventDto.setLastModifiedDateTime(lastModifiedDateTime);
    eventDto.setLocation(location);
    eventDto.setRequiredAttendees(required);
    eventDto.setOptionalAttendees(optional);
    eventDto.setType(type);
    eventDto.setOccurrenceId(occurrenceId);
    eventDto.setSeriesMasterId(seriesMasterId);
    eventDto.setRecurrence(recurrenceString);
    return eventDto;
  }

  private static List<AttendeeAndStatus> getAttendeesByType(
      JSONObject message, String attendeeType) {
    JSONArray attendeesArray = message.getJSONArray("attendees");
    List<AttendeeAndStatus> attendeesList = new ArrayList<>();

    // Loop through each attendee
    for (int i = 0; i < attendeesArray.length(); i++) {
      JSONObject attendeeObject = attendeesArray.getJSONObject(i);

      // Check if the attendee type matches the requested type (e.g., "required" or "optional")
      if (attendeeObject.getString(EmailConstants.TYPE).equalsIgnoreCase(attendeeType)) {

        // Extract email address and response status
        String emailAddress = attendeeObject.getJSONObject("emailAddress").getString("address");
        String responseStatus = attendeeObject.getJSONObject(STATUS).getString("response");

        // Create a new AttendeeAndStatus object and add it to the list
        attendeesList.add(new AttendeeAndStatus(emailAddress, responseStatus));
      }
    }

    return attendeesList;
  }

  private List<String> getRecipients(JSONObject message, String recipientType) {
    JSONArray toRecipientsArray = message.getJSONArray(recipientType);
    List<String> toRecipients = new ArrayList<>();
    for (int j = 0; j < toRecipientsArray.length(); j++) {
      String recipientAddress = null;
      if (toRecipientsArray
          .getJSONObject(j)
          .getJSONObject(EmailConstants.EMAIL_ADDRESS)
          .has(EmailConstants.ADDRESS)) {
        recipientAddress =
            toRecipientsArray
                .getJSONObject(j)
                .getJSONObject(EmailConstants.EMAIL_ADDRESS)
                .getString(EmailConstants.ADDRESS);
      }
      toRecipients.add(recipientAddress);
    }
    return toRecipients;
  }

  private Date getDateFromJSONObject(JSONObject jsonObject, String key) {
    Date date = null;
    if (jsonObject.has(key)) {
      String dueDateTime = jsonObject.getJSONObject(key).getString(EmailConstants.DATE_TIME);
      String timeZone = jsonObject.getJSONObject(key).getString(EmailConstants.TIME_ZONE);
      date = parseDateFromString(dueDateTime, timeZone);
    }
    return date;
  }

  private Date parseDateFromString(String date, String timeZone) {
    // Define the formatter for the input date-time string
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS");

    // Parse the date-time string to a ZonedDateTime in the given time zone
    ZonedDateTime zonedDateTime =
        ZonedDateTime.parse(date, formatter.withZone(ZoneId.of(timeZone)));

    // Convert the ZonedDateTime to an Instant and then to java.util.Date
    Instant instant = zonedDateTime.toInstant();
    return Date.from(instant);
  }

  @Override
  public List<Meeting> getAvailableMeetingSlots(
          List<String> emails, String startDateTime, String endDateTime, int slotDuration) {
    log.debug(
        "Inside @method getAvailableMeetingSlots. @param: emails -> {}, startDateTime -> {}, endDateTime -> {}",
        emails,
        startDateTime,
        endDateTime);
    try {
      if (startDateTime == null || endDateTime == null) {
        String email = userContextHolder.getCurrentUser().getEmail();
        EmailUser user = emailUserDao.findByEmail(email);
        String userId = user.getEmail();
        EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(userId);
        startDateTime = DateUtils.convertToUTCString(LocalTime.now(), preferences.getTimeZone(), 0);
        endDateTime =
            DateUtils.convertToUTCString(preferences.getCheckout(), preferences.getTimeZone(), 0);
      }

      Map<String, List<Meeting>> result = new HashMap<>();
      for (String email : emails) {
        List<EventDto> calendarEvents =
            getCalendarEventsV1(email, startDateTime, endDateTime, null);
        // Handle case where calendarEvents is empty
        if (calendarEvents == null || calendarEvents.isEmpty()) {
          log.debug("No calendar events found for email: {}", email);
          result.put(email, new ArrayList<>()); // Add empty list for this email
          continue; // Skip to the next email
        }
        List<Meeting> list = new ArrayList<>();
        for (EventDto eventDto : calendarEvents) {
          Meeting meeting = new Meeting();
          meeting.setStartTime(eventDto.getMeetingStartTime());
          meeting.setEndTime(eventDto.getMeetingEndTime());
          list.add(meeting);
        }
        result.put(email, list);
      }
      Date start = convertStringToDate(startDateTime);
      Date end = convertStringToDate(endDateTime);
      long durationMillis = (long) slotDuration * MINUTES_PER_HOUR * MILLIS_PER_SECOND;
      return findAvailableTimeSlots(result, start, end, durationMillis);
    } catch (Exception e) {
      log.error("Error inside @method getAvailableSlot : {}", e.getMessage(), e);
    }
    return null;
  }

  private Date convertStringToDate(String dateTimeString) {
    Instant instant = Instant.parse(dateTimeString);
    return Date.from(instant);
  }

  public List<Meeting> findAvailableTimeSlots(
          Map<String, List<Meeting>> meetings, Date from, Date till, long meetingDurationMillis) {
    List<Meeting> availableSlots = new ArrayList<>();
    // Merge all meetings into one list and sort by start time
    List<Meeting> allMeetings = new ArrayList<>();
    for (List<Meeting> userMeetings : meetings.values()) {
      allMeetings.addAll(userMeetings);
    }
    allMeetings.sort(Comparator.comparing(Meeting::getStartTime));
    // Initialize the currentStart to the 'from' date
    Date currentStart = from;
    for (Meeting meeting : allMeetings) {
      // Check if the gap between currentStart and the next meeting's startTime is enough
      if (meeting.getStartTime().after(currentStart)) {
        long gap = meeting.getStartTime().getTime() - currentStart.getTime();
        while (gap >= meetingDurationMillis) {
          Date potentialEnd = new Date(currentStart.getTime() + meetingDurationMillis);

          // Check if currentStart or potentialEnd goes beyond 'till'
          if (currentStart.after(till) || potentialEnd.after(till)) {
            break; // Exit if it exceeds 'till'
          }
          // Add the slot if valid
          availableSlots.add(new Meeting(currentStart, potentialEnd));

          // Move to the next slot
          currentStart = potentialEnd;

          // Recalculate the gap for the next iteration
          gap = meeting.getStartTime().getTime() - currentStart.getTime();
        }
      }

      // Move currentStart to the end of the current meeting if it's later
      if (meeting.getEndTime().after(currentStart)) {
        currentStart = meeting.getEndTime();
      }
    }
    // Check the time slot after the last meeting until 'till' time
    while (currentStart.before(till)
        && (till.getTime() - currentStart.getTime() >= meetingDurationMillis)) {
      Date potentialEnd = new Date(currentStart.getTime() + meetingDurationMillis);
      if (potentialEnd.after(till)) {
        break;
      }
      availableSlots.add(new Meeting(currentStart, potentialEnd));
      currentStart = potentialEnd;
    }

    return availableSlots;
  }

  @Override
  public Map<String, Object> updateUserDetails(GraphUserDto graphUserDto) {
    log.debug("Inside @method updateUserDetails");

    String email = userContextHolder.getCurrentUser().getEmail();
    String url = String.format("https://graph.microsoft.com/v1.0/users/%s", email);
    log.debug("PATCH user details to: {}", url);
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      HttpPatch patchRequest = new HttpPatch(url);
      patchRequest.setHeader(
          EmailConstants.AUTHORIZATION,
          EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
      patchRequest.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

      // Convert DTO to JSON
      JSONObject json = new JSONObject();
      if (graphUserDto.getGivenName() != null) json.put("givenName", graphUserDto.getGivenName());
      if (graphUserDto.getSurname() != null) json.put("surname", graphUserDto.getSurname());
      if (graphUserDto.getDepartment() != null) {
        json.put("department", graphUserDto.getDepartment());
      }
      if (graphUserDto.getOfficeLocation() != null) {
        json.put("officeLocation", graphUserDto.getOfficeLocation());
      }
      if (graphUserDto.getEmployeeType() != null) {
        json.put("employeeType", graphUserDto.getEmployeeType());
      }
      if (graphUserDto.getEmployeeHireDate() != null) {
        json.put("employeeHireDate", graphUserDto.getEmployeeHireDate());
      }
      if (graphUserDto.getBusinessPhones() != null) {
        json.put("businessPhones", graphUserDto.getBusinessPhones());
      }
      if (graphUserDto.getMail() != null) {
        json.put("mail", graphUserDto.getMail());
      }
      if (graphUserDto.getEmployeeId() != null) {
        json.put("employeeId", graphUserDto.getEmployeeId());
      }
      if (graphUserDto.getJobTitle() != null) {
        json.put("jobTitle", graphUserDto.getJobTitle());
      }

      patchRequest.setEntity(
          new StringEntity(
              json.toString(), ContentType.APPLICATION_JSON.withCharset(StandardCharsets.UTF_8)));

      try (CloseableHttpResponse response = httpClient.execute(patchRequest)) {
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody =
            response.getEntity() != null ? EntityUtils.toString(response.getEntity()) : "";
        if (statusCode == HTTP_STATUS_NO_CONTENT) { // No Content, success
          // If managerEmail is present, update manager
          if (graphUserDto.getManagerEmail() != null && !graphUserDto.getManagerEmail().isBlank()) {
            String managerUrl =
                String.format("https://graph.microsoft.com/v1.0/users/%s/manager/$ref", email);
            JSONObject managerJson = new JSONObject();
            managerJson.put(
                "@odata.id",
                String.format(
                    "https://graph.microsoft.com/v1.0/users/%s", graphUserDto.getManagerEmail()));
            HttpPut putRequest = new HttpPut(managerUrl);
            putRequest.setHeader(
                EmailConstants.AUTHORIZATION,
                EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
            putRequest.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);
            putRequest.setEntity(
                new StringEntity(
                    managerJson.toString(),
                    ContentType.APPLICATION_JSON.withCharset(StandardCharsets.UTF_8)));
            try (CloseableHttpResponse putResponse = httpClient.execute(putRequest)) {
              int putStatus = putResponse.getStatusLine().getStatusCode();
              String putBody =
                  putResponse.getEntity() != null
                      ? EntityUtils.toString(putResponse.getEntity())
                      : "";
              if (putStatus == HTTP_STATUS_NO_CONTENT) {
                return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS, "manager", "updated");
              } else {
                log.error("Failed to update manager. Status: {}, Body: {}", putStatus, putBody);
                return Map.of(
                    EmailConstants.RESULT,
                    EmailConstants.SUCCESS,
                    "manager",
                    FAILED,
                    "managerStatus",
                    putStatus,
                    "managerBody",
                    putBody);
              }
            }
          } else {
            return Map.of(EmailConstants.RESULT, EmailConstants.SUCCESS);
          }
        } else {
          log.error("Failed to update user. Status: {}, Body: {}", statusCode, responseBody);
          return Map.of(
                  EmailConstants.RESULT, EmailConstants.FAILED, EmailConstants.STATUS, statusCode, EmailConstants.BODY, responseBody);
        }
      }
    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      log.warn("Thread was interrupted while updating user details", ie);
      throw new BusinessException("Thread interrupted while updating user details", ie);
    } catch (Exception e) {
      log.error("Exception while updating user details: {}", e.getMessage(), e);
      return Map.of(EmailConstants.RESULT, EmailConstants.FAILED, EmailConstants.ERROR, e.getMessage());
    }
  }

  @Override
  public Map<String, Object> createUser(Map<String, Object> userRequest) {
    log.debug("Inside @method createUser");
    String url = "https://graph.microsoft.com/v1.0/users";
    log.debug("URL to createUser : {}", url);
    Map<String, Object> result = new HashMap<>();
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      JSONObject json = new JSONObject(userRequest);
      HttpPost post = new HttpPost(url);
      post.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
      post.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);
      post.setEntity(new StringEntity(json.toString(), ContentType.APPLICATION_JSON.withCharset(StandardCharsets.UTF_8)));
      try (CloseableHttpResponse response = httpClient.execute(post)) {
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = response.getEntity() != null ? EntityUtils.toString(response.getEntity()) : "";
        result.put(EmailConstants.STATUS, statusCode);
        result.put(EmailConstants.BODY, responseBody);
        if (statusCode == HTTP_STATUS_CREATED) {
          result.put(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } else {
          log.error("error in creating user in graph : {}", responseBody);
          result.put(EmailConstants.RESULT, EmailConstants.FAILED);
          result.put(EmailConstants.ERROR, responseBody);
        }
      }
    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      log.warn("Thread was interrupted while create user", ie);
      throw new BusinessException("Thread interrupted while create user", ie);
    } catch (Exception e) {
      log.error("Error while creating user: {}", e.getMessage(), e);
      result.put(EmailConstants.RESULT, EmailConstants.FAILED);
      result.put(EmailConstants.ERROR, e.getMessage());
    }
    return result;
  }

  @Override
  public Map<String, Object> assignLicenseToUser(String userPrincipalName) {
    log.debug("Inside @method assignLicenseToUser. @param: userPrincipalName -> {}", userPrincipalName);
    Map<String, Object> result = new HashMap<>();
    String url = String.format("https://graph.microsoft.com/v1.0/users/%s/assignLicense", userPrincipalName);
    log.debug("URL to assignLicenseToUser : {}", url);
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      // Build JSON body
      JSONObject body = new JSONObject();
      JSONArray addLicenses = new JSONArray();
      for (String skuId : licenseSkuIds) {
        JSONObject skuObj = new JSONObject();
        skuObj.put("skuId", skuId);
        addLicenses.put(skuObj);
      }
      body.put("addLicenses", addLicenses);
      body.put("removeLicenses", List.of());

      HttpPost post = new HttpPost(url);
      post.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
      post.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);
      post.setEntity(new StringEntity(body.toString(), ContentType.APPLICATION_JSON.withCharset(StandardCharsets.UTF_8)));
      try (CloseableHttpResponse response = httpClient.execute(post)) {
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = response.getEntity() != null ? EntityUtils.toString(response.getEntity()) : "";
        result.put(STATUS, statusCode);
        result.put("body", responseBody);
        if (statusCode == HTTP_STATUS_OK) {
          result.put(EmailConstants.RESULT, EmailConstants.SUCCESS);
        } else {
          log.error("error in assigning license to user : {}", responseBody);
          result.put(EmailConstants.RESULT, EmailConstants.FAILED);
          result.put(EmailConstants.ERROR, responseBody);
        }
      }
    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      log.warn("Thread was interrupted while assigning license to user", ie);
      throw new BusinessException("Thread interrupted while assigning license to user", ie);
    } catch (Exception e) {
      log.error("Error while assigning license: {}", e.getMessage(), e);
      result.put(EmailConstants.RESULT, EmailConstants.FAILED);
      result.put(EmailConstants.ERROR, e.getMessage());
    }
    return result;
  }

  /**
   * Set automatic replies settings for a user via Microsoft Graph API.
   *
   * @param userId The user ID or email address
   * @param request The automatic replies configuration request
   * @return Map containing the result of the operation
   * @throws Exception if there's an error setting the configuration
   */
  @Override
  public Map<String, Object> setAutomaticRepliesSettings(String userId, SetAutomaticRepliesRequest request) throws Exception {
    log.debug("Inside @method setAutomaticRepliesSettings. @param: userId -> {}, request -> {}", userId, request);

    String url = GRAPH_API_USERS_BASE_URL + userId + "/mailboxSettings/automaticRepliesSetting";
    log.debug("URL to set automatic replies settings: {}", url);

    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      URIBuilder uriBuilder = new URIBuilder(url);
      URI uri = uriBuilder.build();

      HttpPatch patchRequest = new HttpPatch(uri);
      patchRequest.setHeader(
          EmailConstants.AUTHORIZATION,
          EmailConstants.BEARER + tokenUtils.getAccessToken(userContextHolder));
      patchRequest.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

      String jsonPayload = convertRequestToMailboxSettingsJson(request);
      log.debug("JSON payload for automatic replies settings: {}", jsonPayload);

      StringEntity entity = new StringEntity(jsonPayload, ContentType.APPLICATION_JSON.withCharset(StandardCharsets.UTF_8));
      patchRequest.setEntity(entity);

      try (CloseableHttpResponse response = httpClient.execute(patchRequest)) {
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = response.getEntity() != null ? EntityUtils.toString(response.getEntity()) : "";

        log.debug("Response status: {}, body: {}", statusCode, responseBody);

        if (statusCode == HTTP_STATUS_OK) {
          return Map.of(
              EmailConstants.RESULT, EmailConstants.SUCCESS,
              EmailConstants.MESSAGE, "Automatic replies settings updated successfully"
          );
        } else {
          log.error("Failed to set automatic replies settings. Status: {}, Body: {}", statusCode, responseBody);
          
          String graphApiErrorMessage = responseBody;
          try {
            if (responseBody != null && !responseBody.trim().isEmpty()) {
              JSONObject errorJson = new JSONObject(responseBody);
              if (errorJson.has("error")) {
                JSONObject error = errorJson.getJSONObject("error");
                if (error.has("message")) {
                  graphApiErrorMessage = error.getString("message");
                } else if (error.has("code")) {
                  graphApiErrorMessage = error.getString("code");
                }
              }
            }
          } catch (Exception e) {
            log.warn("Could not parse Graph API error response, using raw response: {}", e.getMessage());
          }
          
          return Map.of(
              EmailConstants.RESULT, EmailConstants.FAILED,
              EmailConstants.STATUS, statusCode,
              EmailConstants.ERROR, graphApiErrorMessage
          );
        }
      }
    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      log.warn("Thread was interrupted while setting automatic replies settings", ie);
      throw new BusinessException("Thread interrupted while setting automatic replies settings", ie);
    } catch (Exception e) {
      log.error("Error while setting automatic replies settings: {}", e.getMessage(), e);
      throw new ApiException(e);
    }
  }

  /**
   * Convert SetAutomaticRepliesRequest to JSON string for Microsoft Graph API.
   * Wraps the automatic replies settings in the mailboxSettings structure.
   *
   * @param request The request object
   * @return JSON string representation
   */
  private String convertRequestToMailboxSettingsJson(SetAutomaticRepliesRequest request) {
    try {
      JSONObject automaticRepliesObject = new JSONObject();
      automaticRepliesObject.put("status", request.getStatus());

      if (request.getExternalReplyMessage() != null && !request.getExternalReplyMessage().trim().isEmpty()) {
        automaticRepliesObject.put("externalReplyMessage", request.getExternalReplyMessage());
      }

      if (request.getInternalReplyMessage() != null && !request.getInternalReplyMessage().trim().isEmpty()) {
        automaticRepliesObject.put("internalReplyMessage", request.getInternalReplyMessage());
      }

      if (request.getScheduledStartDateTime() != null) {
        JSONObject startDateTime = new JSONObject();
        startDateTime.put("dateTime", request.getScheduledStartDateTime().getDateTime());
        startDateTime.put("timeZone", request.getScheduledStartDateTime().getTimeZone());
        automaticRepliesObject.put("scheduledStartDateTime", startDateTime);
      }

      if (request.getScheduledEndDateTime() != null) {
        JSONObject endDateTime = new JSONObject();
        endDateTime.put("dateTime", request.getScheduledEndDateTime().getDateTime());
        endDateTime.put("timeZone", request.getScheduledEndDateTime().getTimeZone());
        automaticRepliesObject.put("scheduledEndDateTime", endDateTime);
      }

      JSONObject mailboxSettings = new JSONObject();
      mailboxSettings.put("automaticRepliesSetting", automaticRepliesObject);

      return mailboxSettings.toString();
    } catch (JSONException e) {
      log.error("Error converting request to JSON: {}", e.getMessage(), e);
      throw new BusinessException("Error converting request to JSON", e);
    }
  }

  public Map<String, Object> getAutoReplySettingsForUser(String userEmail) throws Exception {
    if (!EmailUtils.checkDomain(userEmail, orgDomainName)) return null;
    log.debug("Inside @method getAutoReplySettingsForUser. @param: userEmail -> {}", userEmail);

    // Create the ExchangeService object
    ExchangeService service = EWSUtils.getServiceObjectForUser(userEmail);

    // Fetch the OutOfOfficeSettings for the user
    OofSettings oofSettings = service.getUserOofSettings(userEmail);

    // Prepare the date format for time zone processing
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS");
    dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

    // Initialize a map to store the response
    Map<String, Object> responseMap = new HashMap<>();

    // Set the external audience (None, Known, All)
    OofExternalAudience externalAudience = oofSettings.getExternalAudience();
    responseMap.put("externalAudience", externalAudience != null ? externalAudience.toString().toLowerCase() : "none");

    // Set the OOF status (Disabled or Scheduled)
    OofState oofState = oofSettings.getState();
    String status = oofState == OofState.Disabled ? "disabled" : "scheduled";
    responseMap.put("status", status);

    // Set the internal and external reply messages
    String internalReply = oofSettings.getInternalReply().getMessage();
    String externalReply = oofSettings.getExternalReply().getMessage();
    responseMap.put("internalReplyMessage", internalReply != null ? internalReply.toString() : "");
    responseMap.put("externalReplyMessage", externalReply != null ? externalReply.toString() : "");

    // Set the scheduled start and end times
    if (oofSettings.getDuration() != null) {
      responseMap.put("scheduledStartDateTime", dateFormat.format(oofSettings.getDuration().getStartTime()));
      responseMap.put("scheduledEndDateTime", dateFormat.format(oofSettings.getDuration().getEndTime()));
      responseMap.put("scheduledStartTimeZone", "UTC");
      responseMap.put("scheduledEndTimeZone", "UTC");
    } else {
      responseMap.put("scheduledStartDateTime", "");
      responseMap.put("scheduledEndDateTime", "");
      responseMap.put("scheduledStartTimeZone", "");
      responseMap.put("scheduledEndTimeZone", "");
    }

    return responseMap;
  }
}
