/*
 * Copyright © 2023–2025 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of VisionWave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of VisionWave.
 */

/**
 * This package contains classes for managing user context and information throughout the Email
 * Agent application.
 *
 * <p>The user information management classes include:
 *
 * <ul>
 *   <li>{@link com.enttribe.emailagent.userinfo.UserInfo} - Represents user information including
 *       user details, preferences, and authentication data
 *   <li>{@link com.enttribe.emailagent.userinfo.UserContext} - Provides context information for the
 *       current user session including user details and session metadata
 *   <li>{@link com.enttribe.emailagent.userinfo.UserContextHolder} - Thread-safe holder for storing
 *       and retrieving user context information across application layers
 * </ul>
 *
 * <p>These classes work together to provide a centralized mechanism for managing user information
 * and context throughout the application lifecycle. They support user authentication, session
 * management, and provide access to user-specific data across different layers of the application.
 *
 * <p>The user context management follows thread-local storage patterns to ensure thread safety and
 * proper isolation of user-specific data in multi-threaded environments.
 *
 * <AUTHOR>
 * @version 1.0.7
 * @since 1.0.0
 */
package com.enttribe.emailagent.userinfo;
