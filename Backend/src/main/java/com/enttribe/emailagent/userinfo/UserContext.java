package com.enttribe.emailagent.userinfo;

import lombok.NoArgsConstructor;

/**
 * The type User context.
 *
 * <AUTHOR>
 */
@NoArgsConstructor
public class UserContext {

  private static final ThreadLocal<UserInfo> userThreadLocal = new ThreadLocal<>();

  public static void setUser(UserInfo user) {
    userThreadLocal.set(user);
  }

  public static UserInfo getUser() {
    return userThreadLocal.get();
  }

  public static void clear() {
    userThreadLocal.remove();
  }
}
