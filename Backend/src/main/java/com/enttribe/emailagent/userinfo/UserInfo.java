package com.enttribe.emailagent.userinfo;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * The type User info.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
public class UserInfo {

  private String id;
  private String email;
  private String customerName;
  private LocalDateTime expiration;
}
