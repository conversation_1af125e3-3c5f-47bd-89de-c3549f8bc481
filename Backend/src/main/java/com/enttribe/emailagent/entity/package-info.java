/*
 * Copyright © 2023–2025 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of VisionWave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of VisionWave.
 */

/**
 * This package contains JPA entity classes that represent the data model for the Email Agent
 * application.
 *
 * <p>The entities in this package are used to map database tables to Java objects and provide the
 * foundation for data persistence operations.
 *
 * <p>Key entities include:
 *
 * <ul>
 *   <li>{@link com.enttribe.emailagent.entity.EmailPreferences} - Represents user email preferences
 *       and configuration settings including display preferences, notification settings, and
 *       meeting-related configurations.
 *   <li>{@link com.enttribe.emailagent.entity.EmailUser} - Represents user information including
 *       email addresses, contact details, and user metadata.
 * </ul>
 *
 * <p>All entities in this package use Jakarta Persistence annotations for ORM mapping and include
 * Lombok annotations for reducing boilerplate code.
 *
 * <AUTHOR>
 * @version 1.0.7
 * @since 1.0.0
 */
package com.enttribe.emailagent.entity;
