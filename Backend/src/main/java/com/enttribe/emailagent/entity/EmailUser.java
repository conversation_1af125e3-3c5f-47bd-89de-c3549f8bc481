package com.enttribe.emailagent.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * The type Email user.
 *
 * <AUTHOR> Sonsale
 */
@Entity
@Getter
@Setter
@Table(name = "EMAIL_USER")
@ToString
public class EmailUser {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ID")
  private Integer id;

  @Column(name = "BATCH_ID")
  private String batchId;

  @Column(name = "EMAIL")
  private String email;

  @Column(name = "PHONE_NUMBER", length = 20)
  private String phoneNumber;

  @Column(name = "TYPE", length = 50)
  private String type = "Office365";

  @Column(name = "DELETED")
  private Boolean deleted = false;

  @Column(name = "NAME")
  private String name;

  @Column(name = "USER_OID")
  private String userOID;

  @Column(name = "QUEUE_NAME")
  private String queueName;

  @Column(name = "CREATED_TIME")
  private Date createdTime;

  @Column(name = "MODIFIED_TIME")
  private Date modifiedTime;

  @Column(name = "USER_ID")
  private String userId;
}
