package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.constant.APIConstants;
import com.enttribe.emailagent.constant.EmailConstants;
import com.enttribe.emailagent.dao.EmailPreferencesDao;
import com.enttribe.emailagent.dto.AvailabilityResponse;
import com.enttribe.emailagent.dto.AvailableSlots;
import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.dto.ForwardEventDto;
import com.enttribe.emailagent.dto.GraphUserDto;
import com.enttribe.emailagent.dto.Meeting;
import com.enttribe.emailagent.dto.SetAutomaticRepliesRequest;
import com.enttribe.emailagent.entity.EmailPreferences;
import com.enttribe.emailagent.exception.ApiException;
import com.enttribe.emailagent.service.EventService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.CommonUtils;
import com.enttribe.emailagent.utils.DateUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController("graphIntegrationRest")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/emailservice")
@RequiredArgsConstructor
@Slf4j
public class GraphIntegrationRest {

  private static final int DEFAULT_AVAILABILITY_SLOT_DURATION = 30;
  private final EventService eventService;
  private final EmailPreferencesDao preferencesDao;
  private final UserContextHolder userContextHolder;

  @GetMapping("/ping")
  public String sayHello() {
    return "I am alive";
  }

  @PostMapping("/v1/getAvailableMeetingSlots")
  @Operation(
      summary = "Get available meeting slots (v1)",
      tags = "Meeting",
      description =
          "Retrieve available meeting slots based on the provided request data using version 1 of the API.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_GET_AVAILABLE_MEETING_SLOTS_V1}))
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "Available meeting slots retrieved successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public List<Meeting> getAvailableMeetingSlots(@RequestBody Map<String, Object> request)
      throws Exception {

    List<String> emails = (List<String>) request.get(EmailConstants.REQUIRED_ATTENDEES);
    String startDateTime = (String) request.get(EmailConstants.START_TIME);
    String endDateTime = (String) request.get(EmailConstants.END_TIME);
    Integer slotDuration = (Integer) request.get(EmailConstants.SLOT_DURATION);
    if (slotDuration == null) slotDuration = DEFAULT_AVAILABILITY_SLOT_DURATION;

    if (startDateTime != null && !startDateTime.endsWith("Z")) {
      EmailPreferences preferences =
          preferencesDao.getEmailPreferencesByUserId(userContextHolder.getCurrentUser().getEmail());
      startDateTime = DateUtils.convertToUTCString(startDateTime, preferences.getTimeZone());
      endDateTime = DateUtils.convertToUTCString(endDateTime, preferences.getTimeZone());
    }

    return eventService.getAvailableMeetingSlots(emails, startDateTime, endDateTime, slotDuration);
  }

  @PostMapping(path = "/getCalendarEvents")
  @Operation(
      summary = "Get calendar events",
      tags = "Calendar",
      description = "Retrieve calendar events based on the details provided in the request body.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_GET_CALENDAR_EVENTS}))
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Calendar events retrieved successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public Map<String, Object> getCalendarEvents(@RequestBody Map<String, String> request){

    String startDateTime = request.get(EmailConstants.START_DATE_TIME);
    String endDateTime = request.get(EmailConstants.END_DATE_TIME);
    String subject = request.get(EmailConstants.SUBJECT);

    String email = userContextHolder.getCurrentUser().getEmail();
    List<EventDto> calendarEvents = eventService.getCalendarEventsV1(email, startDateTime, endDateTime, subject);

    Map<String, Object> response = new HashMap<>();
    response.put(EmailConstants.MEETING_COUNT, calendarEvents.size());
    response.put(EmailConstants.MEETINGS, calendarEvents);
    return response;
  }

  @GetMapping(path = "/declineMeeting")
  @Operation(
      summary = "Decline meeting",
      tags = "Meeting",
      description = "Decline a meeting invitation using the specified event ID.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_DECLINE_MEETING}))
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Meeting declined successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public Map<String, String> declineMeeting(@RequestParam String eventId) throws Exception {
    log.debug("Inside @method declineMeeting");

    eventId = eventId.replace("/", "-");
    return eventService.declineMeeting(eventId);
  }

  @GetMapping(path = "/acceptMeeting")
  @Operation(
      summary = "Accept meeting",
      tags = "Meeting",
      description = "Accept a meeting invitation using the specified event ID.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_ACCEPT_MEETING}))
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Meeting accepted successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public Map<String, String> acceptMeeting(@RequestParam String eventId) throws Exception {
    log.debug("Inside @method acceptMeeting");

    eventId = eventId.replace("/", "-");
    return eventService.acceptMeeting(eventId);
  }

  @GetMapping(path = "/tentativelyAccept")
  @Operation(
      summary = "Tentatively accept meeting",
      tags = "Meeting",
      description = "Tentatively accept a meeting invitation using the specified event ID.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_TENTATIVELY_ACCEPT_MEETING}))
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "Meeting tentatively accepted successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public Map<String, String> tentativelyAcceptMeeting(@RequestParam String eventId)
      throws Exception {
    log.debug("Inside @method tentativelyAcceptMeeting");
    eventId = eventId.replace("/", "-");

    return eventService.tentativelyAccept(eventId);
  }

  @PostMapping(path = "/scheduleEvent")
  @Operation(
      summary = "Schedule event",
      tags = "Event",
      description = "Schedule an event based on the provided request body.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_SCHEDULE_EVENT}))
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Event scheduled successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public Map<String, Object> scheduleEvent(@RequestBody Map<String, Object> jsonBody) {
    log.debug("Inside @method scheduleEvent @param : jsonBody -> {}", jsonBody);
    String email = userContextHolder.getCurrentUser().getEmail();

    String timeZone = (String) jsonBody.get(EmailConstants.TIME_ZONE);
    if (timeZone == null || timeZone.isBlank()) {
      EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
      timeZone = preferences.getTimeZone();
      jsonBody.put(EmailConstants.TIME_ZONE, timeZone);
    }

    String meetingType = (String) jsonBody.get(EmailConstants.MEETING_TYPE);
    if (meetingType == null || meetingType.isBlank()) {
      jsonBody.put(EmailConstants.MEETING_TYPE, EmailConstants.TEAMS);
    }
    return eventService.scheduleEvent(email, jsonBody);
  }

  @PostMapping(path = "/rescheduleEvent")
  @Operation(
      summary = "rescheduleEvent",
      tags = "Email",
      description = "Api used to reschedule event",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_RESCHEDULE_EVENT}))
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Event rescheduled successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public Map<String, String> rescheduleEvent(@RequestBody Map<String, String> request) {
    log.debug("Inside @method rescheduleEvent.");
    return eventService.rescheduleEvent(request);
  }

  @PostMapping("/getEventDetailsBySubjectAndTime")
  @Operation(
      summary = "Get available slots and conflicts (v1)",
      tags = "Meeting",
      description =
          "Retrieve available meeting slots and any scheduling conflicts based on the provided request data using the getSchedule API.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_GET_EVENT_BY_SUBJECT_AND_TIME}))
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "Available slots and conflicts retrieved successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public List<EventDto> getEventDetailsBySubjectAndTime(@RequestBody Map<String, String> request) {
    log.debug("Inside @method getEventDetailsBySubjectAndTime. request : {}", request);
    String email = userContextHolder.getCurrentUser().getEmail();
    String subject = request.get(EmailConstants.SUBJECT);
    String startDateTime = request.get(EmailConstants.START_DATE_TIME);
    String endDateTime = request.get(EmailConstants.END_DATE_TIME);
    return eventService.getEventDetailsBySubjectAndTime(
        email, subject, startDateTime, endDateTime);
  }

    @PostMapping("/getEventDetails")
    @Operation(
            summary = "Get event details",
            tags = "Meeting",
            description =
                    "Retrieve event details based on the provided request data.",
            security =
            @SecurityRequirement(
                    name = APIConstants.DEFAULT,
                    scopes = {APIConstants.ROLE_API_GET_EVENT_DETAILS}))
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Available slots and conflicts retrieved successfully."),
                    @ApiResponse(responseCode = "500", description = "Internal server error.")
            })
    public List<EventDto> getEventDetails(@RequestBody Map<String, String> request) {
        log.debug("Inside @method getEventDetails. request : {}", request);
        String email = userContextHolder.getCurrentUser().getEmail();
        String eventId = request.get(EmailConstants.EVENT_ID);
        String startDateTime = request.get(EmailConstants.START_DATE_TIME);
        String endDateTime = request.get(EmailConstants.END_DATE_TIME);
        return eventService.getEventDetails(
                email, eventId, startDateTime, endDateTime);
    }

  @PostMapping("/v1/getAvailableSlotsAndConflict")
  @Operation(
      summary = "Get available slots and conflicts (v1)",
      tags = "Meeting",
      description =
          "Retrieve available meeting slots and any scheduling conflicts based on the provided request data using the getSchedule API.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_GET_AVAILABLE_SLOTS_AND_CONFLICT_V1}))
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "Available slots and conflicts retrieved successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public AvailableSlots getAvailableSlotsAndConflictV1(@RequestBody Map<String, Object> request)
      throws Exception {
    List<String> emails = (List<String>) request.get(EmailConstants.REQUIRED_ATTENDEES);
    String startDateTime = (String) request.get(EmailConstants.START_TIME);
    String endDateTime = (String) request.get(EmailConstants.END_TIME);
    EmailPreferences preferences =
        preferencesDao.getEmailPreferencesByUserId(userContextHolder.getCurrentUser().getEmail());
    String timeZone = preferences.getTimeZone();
    Boolean workingFlag = (Boolean) request.get(EmailConstants.WORKING_FLAG);
    Integer slotDuration = (Integer) request.get(EmailConstants.SLOT_DURATION);
    if (slotDuration == null) slotDuration = DEFAULT_AVAILABILITY_SLOT_DURATION;
    if (startDateTime != null && endDateTime != null && !startDateTime.endsWith("Z")) {
      startDateTime = DateUtils.convertToUTCString(startDateTime, timeZone);
      endDateTime = DateUtils.convertToUTCString(endDateTime, timeZone);
    }

    return eventService.getAvailableSlotsAndConflictV1(
        emails, startDateTime, endDateTime, slotDuration, workingFlag);
  }

  @PostMapping("/v2/getAvailability")
  @Operation(
      summary = "Get availability using getSchedule API",
      tags = "Meeting",
      description =
          "Retrieve availability information for multiple users using the Microsoft Graph getSchedule API.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_GET_AVAILABILITY}))
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "Availability information retrieved successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public AvailabilityResponse getAvailability(@RequestBody Map<String, Object> request) {
    List<String> emails = (List<String>) request.get(EmailConstants.SCHEDULES);
    String startDateTime = (String) request.get(EmailConstants.START_TIME);
    String endDateTime = (String) request.get(EmailConstants.END_TIME);
    Integer slotDuration = (Integer) request.get(EmailConstants.AVAILABILITY_VIEW_INTERVAL);
    if (slotDuration == null) slotDuration = DEFAULT_AVAILABILITY_SLOT_DURATION;
    Boolean workingFlag = (Boolean) request.get(EmailConstants.WORKING_FLAG);

    try {
      return eventService.getAvailability(
          emails, startDateTime, endDateTime, slotDuration, workingFlag);
    } catch (Exception e) {
      log.error("error in /v2/getAvailability : {}", e.getMessage(), e);
      throw new ApiException(e);
    }
  }

  @PostMapping("/getSchedule")
  @Operation(
      summary = "Get getSchedule using graph getSchedule API",
      tags = "Meeting",
      description =
          "Retrieve availability information for multiple users using the Microsoft Graph getSchedule API.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_GET_SCHEDULE}))
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "Get Schedule information retrieved successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public String getSchedule(@RequestBody Map<String, Object> request) {
    List<String> emails = (List<String>) request.get(EmailConstants.SCHEDULES);
    String startDateTime = (String) request.get(EmailConstants.START_TIME);
    String endDateTime = (String) request.get(EmailConstants.END_TIME);
    Integer slotDuration = (Integer) request.get(EmailConstants.AVAILABILITY_VIEW_INTERVAL);
    if (slotDuration == null) slotDuration = DEFAULT_AVAILABILITY_SLOT_DURATION;
    Boolean workingFlag = (Boolean) request.get(EmailConstants.WORKING_FLAG);

    try {
      return eventService.getSchedule(
          emails, startDateTime, endDateTime, slotDuration, workingFlag);
    } catch (Exception e) {
      log.error("error in /getSchedule : {}", e.getMessage(), e);
      throw new ApiException(e);
    }
  }

  @PostMapping("/cancelEvent")
  @Operation(
      summary = "Cancel event",
      tags = "Event",
      description = "Cancel an event based on the provided event ID and comment.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_CANCEL_EVENT}))
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Event cancelled successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public Map<String, String> cancelEvent(@RequestBody Map<String, String> request)
      throws Exception {
    String eventId = request.get(EmailConstants.EVENT_ID);
    String comment = request.get("comment");
    try {
      return eventService.cancelEvent(eventId, comment);
    } catch (Exception e) {
      log.error("error in /cancelEvent : {}", e.getMessage(), e);
      throw new ApiException(e);
    }
  }

  @PostMapping("/forwardEvent")
  @Operation(
      summary = "Forward event",
      tags = "Event",
      description = "Forward a calendar event to multiple recipients with a comment.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_FORWARD_EVENT}))
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Event forwarded successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public Map<String, String> forwardEvent(@RequestBody ForwardEventDto forwardEventDto)
      throws Exception {
    String eventId = forwardEventDto.getEventId();
    List<String> emailIds = forwardEventDto.getEmailIds();
    String comment = forwardEventDto.getComment();
    return eventService.forwardEvent(eventId, emailIds, comment);
  }

  @PostMapping("/getCalendarEventByEventId")
  @Operation(
      summary = "Get calendar event by event ID",
      tags = "Calendar",
      description =
          "Retrieve calendar event details by the specified event ID using Microsoft Graph API.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_GET_CALENDAR_EVENT_BY_EVENT_ID}))
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Event retrieved successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public EventDto getCalendarEventByEventId(@RequestBody Map<String, String> request)
      throws Exception {
    String eventId = request.get(EmailConstants.EVENT_ID);
    if (eventId == null || eventId.isEmpty()) {
      throw new IllegalArgumentException("eventId is required in request body");
    }
    return eventService.getCalendarEventByEventId(eventId);
  }

  @PostMapping(path = "/v1/updateExistingEvent")
  @Operation(
      summary = "Update Existing Event (v1)",
      tags = "Event",
      description =
          "Update an existing event based on the provided request body using version 1 of the API.",
      security =
          @SecurityRequirement(
              name = APIConstants.DEFAULT,
              scopes = {APIConstants.ROLE_API_UPDATE_EXISTING_EVENT}))
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Event updated successfully."),
        @ApiResponse(responseCode = "500", description = "Internal server error.")
      })
  public String updateExistingEvent(@RequestBody Map<String, Object> jsonBody) {
    String email = userContextHolder.getCurrentUser().getEmail();

    String timeZone = (String) jsonBody.get(EmailConstants.TIME_ZONE);
    if (timeZone == null || timeZone.isBlank()) {
      EmailPreferences preferences = preferencesDao.getEmailPreferencesByUserId(email);
      timeZone = preferences.getTimeZone();
      jsonBody.put(EmailConstants.TIME_ZONE, timeZone);
    }

    String meetingType = (String) jsonBody.get(EmailConstants.MEETING_TYPE);
    if (meetingType == null || meetingType.isBlank()) {
      jsonBody.put(EmailConstants.MEETING_TYPE, EmailConstants.TEAMS);
    }

    String eventId = (String) jsonBody.get(EmailConstants.EVENT_ID);

    log.debug("Inside @method /v1/updateExistingEvent. @param : email -> {}", email);

    String meetingRequestJson = CommonUtils.convertToMeetingRequestForUpdateEvent(jsonBody);
    Map<String, Object> result =
        eventService.updateEvent(eventId, email, meetingRequestJson);
    return (String) result.get(EmailConstants.RESULT);
  }

  @PatchMapping("/updateUserDetails")
  @Operation(summary = "Update user details", tags = "User", description = "Update user details in Microsoft Graph.",
          security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_UPDATE_USER_DETAILS}))
  @ApiResponses(value = {
          @ApiResponse(responseCode = "200", description = "User details updated successfully."),
          @ApiResponse(responseCode = "500", description = "Internal server error.")
  })
  public Map<String, Object> updateUserDetails(@RequestBody GraphUserDto graphUserDto) {
    return eventService.updateUserDetails(graphUserDto);
  }

  @PostMapping("/createUser")
  @Operation(summary = "Create a new user in Microsoft Graph", tags = "User", description = "Creates a new user in Microsoft Graph using the provided details.",
          security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_CREATE_USER}))
  @ApiResponses(value = {
          @ApiResponse(responseCode = "201", description = "User created successfully."),
          @ApiResponse(responseCode = "400", description = "Invalid input."),
          @ApiResponse(responseCode = "500", description = "Internal server error.")
  })
  public Map<String, Object> createUser(@RequestBody Map<String, Object> userRequest) {
    return eventService.createUser(userRequest);
  }

  @PostMapping("/assignLicense")
  @Operation(summary = "Assign license to user", tags = "User", description = "Assign a license to a user in Microsoft Graph.",
      security = @SecurityRequirement(name = APIConstants.DEFAULT, scopes = {APIConstants.ROLE_API_ASSIGN_LICENSE}))
  @ApiResponses(value = {
      @ApiResponse(responseCode = "200", description = "License assigned successfully."),
      @ApiResponse(responseCode = "400", description = "Invalid input."),
      @ApiResponse(responseCode = "500", description = "Internal server error.")
  })
  public Map<String, Object> assignLicense(@RequestBody Map<String, Object> request) {
      String userPrincipalName = (String) request.get(EmailConstants.USER_PRINCIPAL_NAME);
      return eventService.assignLicenseToUser(userPrincipalName);
  }

  @PatchMapping("/setAutomaticRepliesSettings")
  @Operation(
          summary = "Set automatic replies settings for specific user",
          tags = "Mailbox",
          description = "Update automatic replies settings for a specific user via Microsoft Graph API.",
          security =
          @SecurityRequirement(
                  name = APIConstants.DEFAULT,
                  scopes = {APIConstants.ROLE_API_SET_AUTOMATIC_REPLIES_SETTINGS}))
  @ApiResponses(
          value = {
                  @ApiResponse(
                          responseCode = "200",
                          description = "Automatic replies settings updated successfully."),
                  @ApiResponse(responseCode = "500", description = "Internal server error.")
          })
  public Map<String, Object> setAutomaticRepliesSettingsForUser(@RequestBody SetAutomaticRepliesRequest request) throws Exception {
    String userId = userContextHolder.getCurrentUser().getEmail();
    log.debug("Inside @method setAutomaticRepliesSettingsForUser. @param: userId -> '{}', request -> {}", userId, request);
    if (userId == null || userId.trim().isEmpty()) {
      throw new IllegalArgumentException("userId parameter cannot be null or empty");
    }
    log.debug("userId: '{}'", userId);
    return eventService.setAutomaticRepliesSettings(userId, request);
  }

}
