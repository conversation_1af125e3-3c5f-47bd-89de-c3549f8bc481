/*
 * Copyright © 2023–2025 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of VisionWave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of VisionWave.
 */

/**
 * This package contains REST controllers that provide HTTP endpoints for the Email Agent
 * application's API.
 *
 * <p>The REST controllers in this package handle HTTP requests and responses for various email and
 * calendar management operations:
 *
 * <ul>
 *   <li>{@link com.enttribe.emailagent.rest.GraphIntegrationRest} - Provides comprehensive REST API
 *       endpoints for Microsoft Graph integration including:
 *       <ul>
 *         <li>Calendar event management (get, create, update, delete, forward)
 *         <li>Meeting scheduling and availability checking
 *         <li>Event response handling (accept, decline, tentative)
 *         <li>User availability and conflict detection
 *         <li>Working hours and time slot calculations
 *         <li>User profile management and updates
 *       </ul>
 * </ul>
 *
 * <p>All REST controllers in this package follow Spring Boot REST conventions and include proper
 * API documentation using OpenAPI/Swagger annotations. They provide secure endpoints with proper
 * authentication and authorization mechanisms, and include comprehensive error handling and
 * response formatting.
 *
 * <p>The controllers coordinate with service layer components to process business logic and return
 * appropriate HTTP responses. They support various HTTP methods (GET, POST, PATCH) and handle
 * different content types including JSON request/response bodies.
 *
 * <AUTHOR>
 * @version 1.0.7
 * @since 1.0.0
 */
package com.enttribe.emailagent.rest;
