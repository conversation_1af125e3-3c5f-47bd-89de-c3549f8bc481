# Use Alpine-based Temurin image
FROM registry.visionwaves.com/alpine-fixed:3.20.3

# Install required packages using apk
RUN apk add --no-cache \
    dumb-init \
    bash \
    vim \
    curl \
    tzdata \
    libstdc++ \
    openssl

# Set timezone
ENV TZ=UTC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Define constants
ENV SSL_VAULT_PATH=/opt/visionwaves/sql_ssl
ARG APP_NAME
ENV SERVICE_PATH=/opt/visionwaves/$APP_NAME

# Create required directories
RUN mkdir -p $SSL_VAULT_PATH $SERVICE_PATH $SERVICE_PATH/logs

# Add application TAR
ADD ./$APP_NAME.tar $SERVICE_PATH

# Clean up Dockerfile (optional)
RUN rm -rf $SERVICE_PATH/Dockerfile

# Create non-root user and give permission
RUN adduser -D -u 1001 visionwaves && \
    chown -R visionwaves:visionwaves /opt/visionwaves

# Switch to non-root user
USER visionwaves

# Set working directory
WORKDIR $SERVICE_PATH

# Entrypoint and command
ENTRYPOINT ["/usr/bin/dumb-init", "--"]
CMD ["sh", "run.sh", "start"]
