spring.application.name=Email-agent

server.port=8088
server.servlet.context-path=/emailagent

logging.level.org.springframework=ERROR
logging.level.root=ERROR
logging.level.org.hibernate=ERROR
logging.level.com.enttribe=DEBUG
spring.output.ansi.enabled=ALWAYS

spring.datasource.url=${MYSQL_URL:************************************************************************************************}
spring.datasource.username=${MYSQL_USERNAME:root}
spring.datasource.password=${MYSQL_CHECKSUM:root}

spring.datasource.driver-class-name=org.mariadb.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

token.refresh.interval=3300000
email.clients.info=${EMAIL_CLIENTS_INFO:ewogICJkZWZhdWx0IjogewogICAgImNsaWVudElkIjogImJaMXpFVEViYmNDeW0xbzhFQXNjdFA0RHVwUUdJL3R3cmxmWjY5dGFkUVFYS3Aza0RLSXlMdkI1czFPK3RLWlgiLAogICAgImNsaWVudFNlY3JldCI6ICJucnRMdGtidzV5djhnSHJIb1VqSjlKMXM4dUVpOTF5TUJtTlUwVmovcjVBNXY3cm80MXZwcUIzeThEZVFvNEV3IiwKICAgICJ0ZW5hbnRJZCI6ICJJNVVzbGk4MTF6YXJ4N0hkU1RJRzRiWTJtOVNra05hMlhJRWRhNlRydjc2UDRsVEM1ajN2VlRka25tSkNaNjFzIgogIH0sCiAgIkJOVFYiOiB7CiAgICAiY2xpZW50SWQiOiAiYloxekVURWJiY0N5bTFvOEVBc2N0UDREdXBRR0kvdHdybGZaNjl0YWRRUVhLcDNrREtJeUx2QjVzMU8rdEtaWCIsCiAgICAiY2xpZW50U2VjcmV0IjogIm5ydEx0a2J3NXl2OGdIckhvVWpKOUoxczh1RWk5MXlNQm1OVTBWai9yNUE1djdybzQxdnBxQjN5OERlUW80RXciLAogICAgInRlbmFudElkIjogIkk1VXNsaTgxMXphcng3SGRTVElHNGJZMm05U2trTmEyWElFZGE2VHJ2NzZQNGxUQzVqM3ZWVGRrbm1KQ1o2MXMiCiAgfQp9}

commons.ai.sdk.is_local=${IS_LOCAL:true}
email.platform=${EMAIL_PLATFORM:ews}

org.domain.name=${ORG_DOMAIN:vision.com,visionwaves.com}
outsiders.events.lookup.allow=${ALLOW_EVENT_LOOKUP:false}
total.meeting.limit=${MEETING_LIMIT:500}

#management.otlp.tracing.endpoint=${TRACING_ENDPOINT:http://jaeger-collector.jaeger.svc.cluster.local:4318/v1/traces}
graph.license.skuIds=${GRAPH_LICENSE_SKU_IDS:f245ecc8-75af-4f8e-b61f-27d8114de5f3}

ews.serviceAccountUsername=${EWS_SERVICE_ACCOUNT_USERNAME:<EMAIL>}
ews.serviceAccountPassword=${EWS_SERVICE_ACCOUNT_PASSWORD:Faizan@#321}
ews.ewsURL=${EWS_URL:https://exchange-server.vision.com/EWS/Exchange.asmx}

zoom.clientId=${ZOOM_CLIENT_ID:ya067IWRRD6WhB58EVQxvQ}
zoom.tokenUrl=${ZOOM_TOKEN_URL:https://zoom.us/oauth/token}
zoom.apiURL=${ZOOM_API_URL:https://api.zoom.us/v2}
zoom.clientSecret=${ZOOM_CLIENT_SECRET:vhVnxUJUTA4HkehH3ELxv3Vv7CGdGDqA}
zoom.accountId=${ZOOM_ACCOUNT_ID:WDPKtUbyT36OI-qIvo5a_g}